# -*- coding: utf-8 -*-
"""
Virtual Keyboard Widget for TridentOS
Provides on-screen keyboard functionality for marine touchscreen devices
"""

from kivy.uix.boxlayout import BoxLayout
from kivy.uix.button import Button
from kivy.uix.label import Label
from kivy.uix.popup import Popup
from kivy.properties import StringProperty, BooleanProperty, ObjectProperty
from kivy.factory import Factory
from kivy.clock import Clock


class VirtualKeyboard(BoxLayout):
    """
    Virtual keyboard widget with marine-optimized layout
    Features:
    - Large buttons for touchscreen use
    - Dark theme for marine environments
    - Support for text input and password input
    - Auto-hide functionality
    """

    target_input = ObjectProperty(None, allownone=True)
    text_input = StringProperty("")
    is_password = BooleanProperty(False)
    is_numeric = BooleanProperty(False)

    def __init__(self, target_input=None, is_password=False, is_numeric=False, **kwargs):
        super(VirtualKeyboard, self).__init__(**kwargs)
        self.target_input = target_input
        self.is_password = is_password
        self.is_numeric = is_numeric
        self.orientation = 'vertical'
        self.size_hint = (1, 0.4)
        self.pos_hint = {'bottom': 1}
        self.spacing = 5
        self.padding = [10, 10, 10, 10]

        # Dark marine theme
        with self.canvas.before:
            from kivy.graphics import Color, RoundedRectangle
            Color(0.05, 0.1, 0.15, 0.95)  # Dark navy with transparency
            self.bg_rect = RoundedRectangle(pos=self.pos, size=self.size, radius=[15])
            self.bind(pos=self.update_bg, size=self.update_bg)

        # Initialize with target input text if available
        if self.target_input and hasattr(self.target_input, 'text'):
            self.text_input = self.target_input.text

        self.build_keyboard()
    
    def update_bg(self, *args):
        """Update background rectangle"""
        self.bg_rect.pos = self.pos
        self.bg_rect.size = self.size
    
    def build_keyboard(self):
        """Build the keyboard layout"""
        self.clear_widgets()
        
        # Text display area
        self.text_display = Label(
            text=self.get_display_text(),
            font_size='20sp',
            size_hint_y=0.2,
            halign='left',
            valign='middle',
            color=(1, 1, 1, 1),
            text_size=(None, None)
        )
        self.add_widget(self.text_display)
        
        if self.is_numeric:
            self.build_numeric_keyboard()
        else:
            self.build_qwerty_keyboard()
        
        # Control buttons
        self.build_control_buttons()
    
    def build_qwerty_keyboard(self):
        """Build QWERTY keyboard layout"""
        # Row 1: Numbers
        row1 = BoxLayout(orientation='horizontal', size_hint_y=0.2, spacing=5)
        numbers = "1234567890"
        for char in numbers:
            btn = self.create_key_button(char)
            row1.add_widget(btn)
        
        # Backspace button
        backspace_btn = self.create_key_button("⌫", action=self.backspace)
        backspace_btn.background_color = (0.8, 0.3, 0.3, 1)
        row1.add_widget(backspace_btn)
        self.add_widget(row1)
        
        # Row 2: QWERTY
        row2 = BoxLayout(orientation='horizontal', size_hint_y=0.2, spacing=5)
        qwerty = "QWERTYUIOP"
        for char in qwerty:
            btn = self.create_key_button(char)
            row2.add_widget(btn)
        self.add_widget(row2)
        
        # Row 3: ASDF
        row3 = BoxLayout(orientation='horizontal', size_hint_y=0.2, spacing=5)
        asdf = "ASDFGHJKL"
        for char in asdf:
            btn = self.create_key_button(char)
            row3.add_widget(btn)
        self.add_widget(row3)
        
        # Row 4: ZXCV with space
        row4 = BoxLayout(orientation='horizontal', size_hint_y=0.2, spacing=5)
        zxcv = "ZXCVBNM"
        for char in zxcv:
            btn = self.create_key_button(char)
            row4.add_widget(btn)
        
        # Space button
        space_btn = self.create_key_button("SPACE", action=lambda: self.add_character(" "))
        space_btn.size_hint_x = 2
        row4.add_widget(space_btn)
        self.add_widget(row4)
    
    def build_numeric_keyboard(self):
        """Build numeric keyboard layout"""
        # Row 1: 7, 8, 9
        row1 = BoxLayout(orientation='horizontal', size_hint_y=0.25, spacing=5)
        for num in "789":
            btn = self.create_key_button(num)
            row1.add_widget(btn)
        
        # Backspace button
        backspace_btn = self.create_key_button("⌫", action=self.backspace)
        backspace_btn.background_color = (0.8, 0.3, 0.3, 1)
        row1.add_widget(backspace_btn)
        self.add_widget(row1)
        
        # Row 2: 4, 5, 6
        row2 = BoxLayout(orientation='horizontal', size_hint_y=0.25, spacing=5)
        for num in "456":
            btn = self.create_key_button(num)
            row2.add_widget(btn)
        
        # Dot button for decimals
        dot_btn = self.create_key_button(".")
        row2.add_widget(dot_btn)
        self.add_widget(row2)
        
        # Row 3: 1, 2, 3
        row3 = BoxLayout(orientation='horizontal', size_hint_y=0.25, spacing=5)
        for num in "123":
            btn = self.create_key_button(num)
            row3.add_widget(btn)
        
        # Clear button
        clear_btn = self.create_key_button("CLR", action=self.clear_text)
        clear_btn.background_color = (0.8, 0.6, 0, 1)
        row3.add_widget(clear_btn)
        self.add_widget(row3)
        
        # Row 4: 0
        row4 = BoxLayout(orientation='horizontal', size_hint_y=0.25, spacing=5)
        zero_btn = self.create_key_button("0")
        zero_btn.size_hint_x = 2
        row4.add_widget(zero_btn)
        
        # Minus button
        minus_btn = self.create_key_button("-")
        row4.add_widget(minus_btn)
        
        # Plus button
        plus_btn = self.create_key_button("+")
        row4.add_widget(plus_btn)
        self.add_widget(row4)
    
    def build_control_buttons(self):
        """Build control buttons (OK, Cancel)"""
        control_row = BoxLayout(orientation='horizontal', size_hint_y=0.15, spacing=10)
        
        # Cancel button
        cancel_btn = Button(
            text="CANCEL",
            font_size='18sp',
            background_normal='',
            background_color=(0.8, 0.3, 0.3, 1)
        )
        cancel_btn.bind(on_press=self.cancel_input)
        control_row.add_widget(cancel_btn)
        
        # OK button
        ok_btn = Button(
            text="OK",
            font_size='18sp',
            background_normal='',
            background_color=(0, 0.7, 0, 1)
        )
        ok_btn.bind(on_press=self.confirm_input)
        control_row.add_widget(ok_btn)
        
        self.add_widget(control_row)
    
    def create_key_button(self, text, action=None):
        """Create a keyboard button"""
        btn = Button(
            text=text,
            font_size='18sp',
            background_normal='',
            background_color=(0.2, 0.4, 0.6, 1),
            color=(1, 1, 1, 1)
        )
        
        if action:
            btn.bind(on_press=lambda x: action())
        else:
            btn.bind(on_press=lambda x: self.add_character(text))
        
        return btn
    
    def add_character(self, char):
        """Add character to input"""
        self.text_input += char
        self.update_display()
    
    def backspace(self):
        """Remove last character"""
        if self.text_input:
            self.text_input = self.text_input[:-1]
            self.update_display()
    
    def clear_text(self):
        """Clear all text"""
        self.text_input = ""
        self.update_display()
    
    def update_display(self):
        """Update text display"""
        self.text_display.text = self.get_display_text()
        self.text_display.text_size = (self.text_display.width, None)
    
    def get_display_text(self):
        """Get display text (masked for passwords)"""
        if self.is_password and self.text_input:
            return "*" * len(self.text_input)
        return self.text_input
    
    def confirm_input(self, *args):
        """Confirm input and close keyboard"""
        if self.target_input and hasattr(self.target_input, 'text'):
            self.target_input.text = self.text_input
            # Trigger any bound events
            if hasattr(self.target_input, 'dispatch'):
                self.target_input.dispatch('on_text_validate')
        self.hide_keyboard()

    def cancel_input(self, *args):
        """Cancel input and close keyboard"""
        self.text_input = ""
        self.hide_keyboard()

    def hide_keyboard(self):
        """Hide the keyboard"""
        if self.parent:
            self.parent.remove_widget(self)
            # Notify parent that keyboard was hidden
            if hasattr(self.parent, 'keyboard_hidden'):
                self.parent.keyboard_hidden()


class VirtualKeyboardPopup(Popup):
    """Popup wrapper for virtual keyboard"""
    
    def __init__(self, target_input=None, is_password=False, is_numeric=False, **kwargs):
        super(VirtualKeyboardPopup, self).__init__(**kwargs)
        
        self.title = "Enter Text"
        self.size_hint = (0.9, 0.7)
        self.auto_dismiss = False
        
        # Create keyboard
        self.keyboard = VirtualKeyboard(
            target_input=target_input,
            is_password=is_password,
            is_numeric=is_numeric
        )
        
        # Override keyboard methods to close popup
        original_confirm = self.keyboard.confirm_input
        original_cancel = self.keyboard.cancel_input
        
        def confirm_and_close(*args):
            original_confirm(*args)
            self.dismiss()
        
        def cancel_and_close(*args):
            original_cancel(*args)
            self.dismiss()
        
        self.keyboard.confirm_input = confirm_and_close
        self.keyboard.cancel_input = cancel_and_close
        
        self.content = self.keyboard


# Register with Factory for use in KV files
Factory.register('VirtualKeyboard', VirtualKeyboard)
Factory.register('VirtualKeyboardPopup', VirtualKeyboardPopup)
