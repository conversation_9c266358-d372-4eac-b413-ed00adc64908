#!/usr/bin/env python3
"""
Diagnostic script for Raspberry Pi 5 detection and GPIO availability.
"""

import platform
import os
import sys

def check_platform_detection():
    """Check various methods of Raspberry Pi detection."""
    print("=== PLATFORM DETECTION DIAGNOSTICS ===")
    
    # Method 1: platform.machine()
    machine = platform.machine()
    print(f"platform.machine(): '{machine}'")
    
    # Method 2: platform.node()
    node = platform.node()
    print(f"platform.node(): '{node}'")
    
    # Method 3: platform.platform()
    platform_info = platform.platform()
    print(f"platform.platform(): '{platform_info}'")
    
    # Method 4: Check /proc/cpuinfo
    try:
        with open('/proc/cpuinfo', 'r') as f:
            cpuinfo = f.read()
            if 'Raspberry Pi' in cpuinfo:
                print("✅ /proc/cpuinfo contains 'Raspberry Pi'")
                # Extract model info
                for line in cpuinfo.split('\n'):
                    if 'Model' in line:
                        print(f"   {line.strip()}")
                    elif 'Hardware' in line:
                        print(f"   {line.strip()}")
                    elif 'Revision' in line:
                        print(f"   {line.strip()}")
            else:
                print("❌ /proc/cpuinfo does not contain 'Raspberry Pi'")
    except FileNotFoundError:
        print("❌ /proc/cpuinfo not found (not Linux)")
    except Exception as e:
        print(f"❌ Error reading /proc/cpuinfo: {e}")
    
    # Method 5: Check /proc/device-tree/model
    try:
        with open('/proc/device-tree/model', 'r') as f:
            model = f.read().strip('\x00')
            print(f"Device tree model: '{model}'")
            if 'Raspberry Pi' in model:
                print("✅ Device tree confirms Raspberry Pi")
            else:
                print("❌ Device tree does not show Raspberry Pi")
    except FileNotFoundError:
        print("❌ /proc/device-tree/model not found")
    except Exception as e:
        print(f"❌ Error reading device tree model: {e}")
    
    # Method 6: Check for GPIO directory
    gpio_paths = ['/sys/class/gpio', '/dev/gpiomem', '/dev/gpiochip0']
    for path in gpio_paths:
        if os.path.exists(path):
            print(f"✅ GPIO path exists: {path}")
        else:
            print(f"❌ GPIO path missing: {path}")
    
    # Current detection logic
    is_rpi_current = machine.startswith('arm') or 'raspberry' in node.lower()
    print(f"\nCurrent detection result: {is_rpi_current}")
    
    return is_rpi_current

def check_gpio_libraries():
    """Check availability of GPIO libraries."""
    print("\n=== GPIO LIBRARIES DIAGNOSTICS ===")
    
    libraries = {
        'gpiozero': None,
        'RPi.GPIO': None,
        'pigpio': None
    }
    
    # Test gpiozero
    try:
        import gpiozero
        libraries['gpiozero'] = gpiozero.__version__
        print(f"✅ gpiozero version: {gpiozero.__version__}")
        
        # Test PiGPIO factory
        try:
            from gpiozero.pins.pigpio import PiGPIOFactory
            print("✅ PiGPIOFactory available")
        except ImportError as e:
            print(f"❌ PiGPIOFactory not available: {e}")
            
    except ImportError as e:
        print(f"❌ gpiozero not available: {e}")
    
    # Test RPi.GPIO
    try:
        import RPi.GPIO as GPIO
        libraries['RPi.GPIO'] = GPIO.VERSION
        print(f"✅ RPi.GPIO version: {GPIO.VERSION}")
    except ImportError as e:
        print(f"❌ RPi.GPIO not available: {e}")
    
    # Test pigpio
    try:
        import pigpio
        libraries['pigpio'] = "available"
        print(f"✅ pigpio available")
    except ImportError as e:
        print(f"❌ pigpio not available: {e}")
    
    return libraries

def check_gpio_permissions():
    """Check GPIO permissions and access."""
    print("\n=== GPIO PERMISSIONS DIAGNOSTICS ===")
    
    import pwd
    import grp
    
    # Check user groups
    try:
        user = pwd.getpwuid(os.getuid()).pw_name
        groups = [grp.getgrgid(g).gr_name for g in os.getgroups()]
        print(f"Current user: {user}")
        print(f"User groups: {', '.join(groups)}")
        
        if 'gpio' in groups:
            print("✅ User is in 'gpio' group")
        else:
            print("❌ User is NOT in 'gpio' group")
            print("   Fix: sudo usermod -a -G gpio $USER")
            
    except Exception as e:
        print(f"❌ Error checking user groups: {e}")
    
    # Check GPIO device permissions
    gpio_devices = ['/dev/gpiomem', '/dev/gpiochip0']
    for device in gpio_devices:
        if os.path.exists(device):
            stat = os.stat(device)
            print(f"✅ {device} exists, permissions: {oct(stat.st_mode)[-3:]}")
        else:
            print(f"❌ {device} does not exist")

def improved_rpi_detection():
    """Improved Raspberry Pi detection logic."""
    print("\n=== IMPROVED DETECTION LOGIC ===")
    
    detection_methods = []
    
    # Method 1: Check /proc/cpuinfo for Raspberry Pi
    try:
        with open('/proc/cpuinfo', 'r') as f:
            if 'Raspberry Pi' in f.read():
                detection_methods.append("cpuinfo")
    except:
        pass
    
    # Method 2: Check device tree model
    try:
        with open('/proc/device-tree/model', 'r') as f:
            if 'Raspberry Pi' in f.read():
                detection_methods.append("device-tree")
    except:
        pass
    
    # Method 3: Check platform info
    if platform.machine().startswith('arm') or platform.machine().startswith('aarch'):
        detection_methods.append("arm-architecture")
    
    # Method 4: Check hostname
    if 'raspberry' in platform.node().lower() or 'rpi' in platform.node().lower():
        detection_methods.append("hostname")
    
    # Method 5: Check for BCM2835/BCM2711/BCM2712 (RPi5 uses BCM2712)
    try:
        with open('/proc/cpuinfo', 'r') as f:
            cpuinfo = f.read()
            if any(chip in cpuinfo for chip in ['BCM2835', 'BCM2711', 'BCM2712']):
                detection_methods.append("bcm-chip")
    except:
        pass
    
    print(f"Detection methods that succeeded: {detection_methods}")
    
    is_rpi = len(detection_methods) > 0
    print(f"Improved detection result: {is_rpi}")
    
    return is_rpi, detection_methods

def test_basic_gpio():
    """Test basic GPIO functionality if available."""
    print("\n=== BASIC GPIO TEST ===")
    
    try:
        # Try gpiozero first
        import gpiozero
        print("Testing with gpiozero...")
        
        # Test LED on pin 18 (safe pin for testing)
        from gpiozero import LED
        test_led = LED(18)
        print("✅ GPIO Zero LED object created successfully")
        test_led.close()
        print("✅ GPIO Zero LED object closed successfully")
        
    except Exception as e:
        print(f"❌ gpiozero test failed: {e}")
        
        try:
            # Try RPi.GPIO as fallback
            import RPi.GPIO as GPIO
            print("Testing with RPi.GPIO...")
            
            GPIO.setmode(GPIO.BCM)
            GPIO.setup(18, GPIO.OUT)
            print("✅ RPi.GPIO setup successful")
            GPIO.cleanup()
            print("✅ RPi.GPIO cleanup successful")
            
        except Exception as e2:
            print(f"❌ RPi.GPIO test failed: {e2}")

def main():
    """Run all diagnostics."""
    print("🔍 Raspberry Pi 5 & GPIO Diagnostics")
    print("=" * 50)
    
    # Platform detection
    is_rpi_current = check_platform_detection()
    
    # GPIO libraries
    libraries = check_gpio_libraries()
    
    # GPIO permissions
    check_gpio_permissions()
    
    # Improved detection
    is_rpi_improved, methods = improved_rpi_detection()
    
    # Basic GPIO test (only if libraries are available)
    if any(libraries.values()):
        test_basic_gpio()
    
    # Summary
    print("\n" + "=" * 50)
    print("📋 DIAGNOSTIC SUMMARY")
    print("=" * 50)
    print(f"Current detection logic: {'✅ PASS' if is_rpi_current else '❌ FAIL'}")
    print(f"Improved detection logic: {'✅ PASS' if is_rpi_improved else '❌ FAIL'}")
    print(f"GPIO libraries available: {'✅ YES' if any(libraries.values()) else '❌ NO'}")
    
    if not is_rpi_current and is_rpi_improved:
        print("\n💡 RECOMMENDATION: Update detection logic")
        print("   The improved detection found Raspberry Pi but current logic didn't")
    
    if not any(libraries.values()):
        print("\n💡 RECOMMENDATION: Install GPIO libraries")
        print("   Run: pip install gpiozero pigpio RPi.GPIO")
    
    print(f"\nDetection methods that worked: {methods}")

if __name__ == "__main__":
    main()
