#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Test script for TridentOS fixes
Tests: Raspberry Pi 5 detection, language packages, script execution
"""

import os
import sys
import platform
import subprocess

# Add current directory to path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_raspberry_pi_detection():
    """Test enhanced Raspberry Pi detection"""
    print("=== Testing Enhanced Raspberry Pi Detection ===")
    
    try:
        from main import is_raspberry_pi
        
        # Test the enhanced detection function
        is_rpi = is_raspberry_pi()
        print(f"Raspberry Pi detected: {is_rpi}")
        
        # Show detection details
        print("\nDetection details:")
        
        # Check /proc/cpuinfo
        try:
            with open('/proc/cpuinfo', 'r') as f:
                cpuinfo = f.read()
                if 'Raspberry Pi' in cpuinfo:
                    print("✅ /proc/cpuinfo contains 'Raspberry Pi'")
                elif any(chip in cpuinfo for chip in ['BCM2835', 'BCM2711', 'BCM2712']):
                    print("✅ BCM chip detected in /proc/cpuinfo")
                else:
                    print("❌ No Raspberry Pi indicators in /proc/cpuinfo")
        except FileNotFoundError:
            print("❌ /proc/cpuinfo not found (not Linux)")
        
        # Check device tree
        try:
            with open('/proc/device-tree/model', 'r') as f:
                model = f.read().strip('\x00')
                if 'Raspberry Pi' in model:
                    print(f"✅ Device tree model: {model}")
                else:
                    print(f"❌ Device tree model: {model}")
        except FileNotFoundError:
            print("❌ /proc/device-tree/model not found")
        
        # Check architecture
        machine = platform.machine().lower()
        print(f"Architecture: {machine}")
        if machine.startswith('arm') or machine.startswith('aarch'):
            print("✅ ARM architecture detected")
        else:
            print("❌ Not ARM architecture")
        
        # Check hostname
        hostname = platform.node().lower()
        print(f"Hostname: {hostname}")
        if 'raspberry' in hostname or 'rpi' in hostname:
            print("✅ Hostname suggests Raspberry Pi")
        else:
            print("❌ Hostname does not suggest Raspberry Pi")
        
        # Check GPIO paths
        gpio_paths = ['/sys/class/gpio', '/dev/gpiomem', '/dev/gpiochip0']
        for path in gpio_paths:
            if os.path.exists(path):
                print(f"✅ GPIO path exists: {path}")
            else:
                print(f"❌ GPIO path missing: {path}")
        
        return True
    except Exception as e:
        print(f"✗ Raspberry Pi detection test error: {e}")
        return False

def test_language_system():
    """Test language translation system"""
    print("\n=== Testing Language Translation System ===")
    
    try:
        from translations import get_translation, get_available_languages
        
        # Test available languages
        languages = get_available_languages()
        print(f"Available languages: {languages}")
        
        if len(languages) >= 4:
            print("✅ All 4 languages available")
        else:
            print(f"❌ Only {len(languages)} languages available")
        
        # Test translations for each language
        test_keys = ["SETTINGS", "Language", "WiFi", "Brightness"]
        
        for lang in languages:
            print(f"\nTesting {lang}:")
            for key in test_keys:
                translation = get_translation(key, lang)
                print(f"  {key} -> {translation}")
                
                # Verify translation is different from key (except English)
                if lang != "English" and translation == key:
                    print(f"  ⚠ Warning: No translation for {key} in {lang}")
        
        # Test MainApp translation method
        try:
            from main import MainApp
            app = MainApp()
            
            # Test get_translation method
            translation = app.get_translation("SETTINGS", "Polski")
            print(f"\nMainApp translation test: SETTINGS -> {translation}")
            
            if translation != "SETTINGS":
                print("✅ MainApp translation method working")
            else:
                print("❌ MainApp translation method not working")
                
        except Exception as e:
            print(f"❌ MainApp translation test error: {e}")
        
        return True
    except Exception as e:
        print(f"✗ Language system test error: {e}")
        return False

def test_script_execution():
    """Test script execution capabilities"""
    print("\n=== Testing Script Execution ===")
    
    try:
        # Test Python script
        print("Testing Python startup script...")
        result = subprocess.run([
            sys.executable, 'start_tridentos.py', '--test'
        ], capture_output=True, text=True, timeout=30)
        
        if result.returncode == 0:
            print("✅ Python startup script test passed")
        else:
            print(f"❌ Python startup script test failed: {result.stderr}")
        
        # Test if bash script exists and is executable
        bash_script = 'start_tridentos.sh'
        if os.path.exists(bash_script):
            print("✅ Bash startup script exists")
            
            # Check if it's executable (on Unix systems)
            if hasattr(os, 'access') and os.access(bash_script, os.X_OK):
                print("✅ Bash script is executable")
            else:
                print("⚠ Bash script may not be executable")
        else:
            print("❌ Bash startup script not found")
        
        # Test Makefile
        makefile = 'Makefile'
        if os.path.exists(makefile):
            print("✅ Makefile exists")
        else:
            print("❌ Makefile not found")
        
        # Test desktop file
        desktop_file = 'TridentOS.desktop'
        if os.path.exists(desktop_file):
            print("✅ Desktop file exists")
        else:
            print("❌ Desktop file not found")
        
        return True
    except Exception as e:
        print(f"✗ Script execution test error: {e}")
        return False

def test_settings_functionality():
    """Test settings screen functionality"""
    print("\n=== Testing Settings Functionality ===")
    
    try:
        from main import SettingsScreen
        
        # Create settings screen
        settings = SettingsScreen()
        print("✅ SettingsScreen created successfully")
        
        # Test language setting
        original_language = settings.language
        settings.set_language("Polski")
        if settings.language == "Polski":
            print("✅ Language setting works")
        else:
            print("❌ Language setting failed")
        
        # Test PIN functionality
        pin_result = settings.set_pin_code("1234")
        if pin_result and settings.pin_code == "1234":
            print("✅ PIN setting works")
        else:
            print("❌ PIN setting failed")
        
        # Test PIN verification
        if settings.verify_pin("1234"):
            print("✅ PIN verification works")
        else:
            print("❌ PIN verification failed")
        
        # Test brightness setting
        settings.set_brightness(75)
        if settings.brightness == 75:
            print("✅ Brightness setting works")
        else:
            print("❌ Brightness setting failed")
        
        # Test WiFi scanning (simulation)
        settings.scan_wifi_networks()
        if len(settings.available_networks) > 0:
            print(f"✅ WiFi scanning works ({len(settings.available_networks)} networks)")
        else:
            print("❌ WiFi scanning failed")
        
        # Test virtual keyboard
        try:
            settings.show_virtual_keyboard(is_password=True, is_numeric=True)
            print("✅ Virtual keyboard can be shown")
            
            settings.hide_virtual_keyboard()
            print("✅ Virtual keyboard can be hidden")
        except Exception as e:
            print(f"❌ Virtual keyboard error: {e}")
        
        return True
    except Exception as e:
        print(f"✗ Settings functionality test error: {e}")
        return False

def test_file_integrity():
    """Test file integrity and completeness"""
    print("\n=== Testing File Integrity ===")
    
    required_files = [
        'main.py',
        'translations.py',
        'widgets/virtual_keyboard.py',
        'ui/settings.kv',
        'start_tridentos.py',
        'start_tridentos.sh',
        'TridentOS.desktop',
        'tridentos.service',
        'Makefile',
        'version.txt'
    ]
    
    missing_files = []
    for file_path in required_files:
        if os.path.exists(file_path):
            print(f"✅ {file_path}")
        else:
            missing_files.append(file_path)
            print(f"❌ {file_path}")
    
    if not missing_files:
        print("✅ All required files present")
        return True
    else:
        print(f"❌ Missing files: {missing_files}")
        return False

def main():
    """Run all fix tests"""
    print("TridentOS Fix Verification Test Suite")
    print("=" * 50)
    
    tests = [
        test_raspberry_pi_detection,
        test_language_system,
        test_script_execution,
        test_settings_functionality,
        test_file_integrity
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        try:
            if test():
                passed += 1
        except Exception as e:
            print(f"✗ Test {test.__name__} failed with exception: {e}")
    
    print("\n" + "=" * 50)
    print(f"Fix Test Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All fixes verified successfully!")
        print("\nTridentOS is ready for Raspberry Pi 5:")
        print("1. Enhanced Raspberry Pi 5 detection ✅")
        print("2. Working language packages ✅")
        print("3. Functional startup scripts ✅")
        print("4. Complete settings functionality ✅")
        print("5. All required files present ✅")
        return 0
    else:
        print("⚠️  Some fixes need attention. Check the output above.")
        return 1

if __name__ == "__main__":
    exit(main())
