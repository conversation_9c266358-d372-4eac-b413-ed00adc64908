# -*- coding: utf-8 -*-
"""
Translation system for TridentOS
Supports multiple languages: English, Polish, German, Russian
"""

# Translation dictionaries
TRANSLATIONS = {
    "English": {
        # Main navigation
        "HOME": "HOME",
        "SETTINGS": "SETTINGS",
        "CLIMATE": "<PERSON><PERSON><PERSON><PERSON><PERSON>",
        "LIGHTNING": "LIGHTNING",
        "BATTERY": "BATTERY",
        "ALARM": "ALARM",
        "ENGINE": "ENGINE",
        "WATER": "WATER",
        "FUEL": "FUEL",
        "AUTOPILOT": "AUTOPILOT",
        
        # Settings categories
        "DISPLAY": "DISPLAY",
        "LANGUAGE": "LANGUAGE",
        "SYSTEM": "SYSTEM",
        "SAFETY": "SAFETY",
        "CONNECTION": "CONNECTION",
        "CALIBRATION": "CALIBRATION",
        
        # Display settings
        "Brightness": "Brightness",
        "Theme Mode": "Theme Mode",
        "Screen Orientation": "Screen Orientation",
        "Day": "Day",
        "Night": "Night",
        "Landscape": "Landscape",
        "Portrait": "Portrait",
        
        # Language settings
        "Language": "Language",
        "Time Format": "Time Format",
        "Units": "Units",
        "Metric": "Metric",
        "Imperial": "Imperial",
        
        # System settings
        "Firmware Version": "Firmware Version",
        "Update Firmware": "Update Firmware",
        "Restart System": "Restart System",
        "Factory Reset": "Factory Reset",
        
        # Safety settings
        "PIN Code Protection": "PIN Code Protection",
        "Enter 4-digit PIN": "Enter 4-digit PIN",
        "Set PIN": "Set PIN",
        "PIN Protection": "PIN Protection",
        "Emergency Contact/Frequency": "Emergency Contact/Frequency",
        "Secure Mode": "Secure Mode",
        
        # Connection settings
        "WiFi": "WiFi",
        "Scan Networks": "Scan Networks",
        "Connected to:": "Connected to:",
        "Not connected": "Not connected",
        "Available Networks:": "Available Networks:",
        "No networks found. Press \"Scan Networks\" to search.": "No networks found. Press \"Scan Networks\" to search.",
        "Password:": "Password:",
        "Enter WiFi password": "Enter WiFi password",
        "Connect": "Connect",
        "Bluetooth": "Bluetooth",
        
        # Common buttons
        "ON": "ON",
        "OFF": "OFF",
        "OK": "OK",
        "CANCEL": "CANCEL",
        "SPACE": "SPACE",
        "CLR": "CLR"
    },
    
    "Polski": {
        # Main navigation
        "HOME": "GŁÓWNA",
        "SETTINGS": "USTAWIENIA",
        "CLIMATE": "KLIMAT",
        "LIGHTNING": "OŚWIETLENIE",
        "BATTERY": "BATERIA",
        "ALARM": "ALARM",
        "ENGINE": "SILNIK",
        "WATER": "WODA",
        "FUEL": "PALIWO",
        "AUTOPILOT": "AUTOPILOT",
        
        # Settings categories
        "DISPLAY": "WYŚWIETLACZ",
        "LANGUAGE": "JĘZYK",
        "SYSTEM": "SYSTEM",
        "SAFETY": "BEZPIECZEŃSTWO",
        "CONNECTION": "POŁĄCZENIE",
        "CALIBRATION": "KALIBRACJA",
        
        # Display settings
        "Brightness": "Jasność",
        "Theme Mode": "Tryb motywu",
        "Screen Orientation": "Orientacja ekranu",
        "Day": "Dzień",
        "Night": "Noc",
        "Landscape": "Poziomo",
        "Portrait": "Pionowo",
        
        # Language settings
        "Language": "Język",
        "Time Format": "Format czasu",
        "Units": "Jednostki",
        "Metric": "Metryczne",
        "Imperial": "Imperialne",
        
        # System settings
        "Firmware Version": "Wersja oprogramowania",
        "Update Firmware": "Aktualizuj oprogramowanie",
        "Restart System": "Restart systemu",
        "Factory Reset": "Reset fabryczny",
        
        # Safety settings
        "PIN Code Protection": "Ochrona kodem PIN",
        "Enter 4-digit PIN": "Wprowadź 4-cyfrowy PIN",
        "Set PIN": "Ustaw PIN",
        "PIN Protection": "Ochrona PIN",
        "Emergency Contact/Frequency": "Kontakt awaryjny/Częstotliwość",
        "Secure Mode": "Tryb bezpieczny",
        
        # Connection settings
        "WiFi": "WiFi",
        "Scan Networks": "Skanuj sieci",
        "Connected to:": "Połączono z:",
        "Not connected": "Nie połączono",
        "Available Networks:": "Dostępne sieci:",
        "No networks found. Press \"Scan Networks\" to search.": "Nie znaleziono sieci. Naciśnij \"Skanuj sieci\" aby wyszukać.",
        "Password:": "Hasło:",
        "Enter WiFi password": "Wprowadź hasło WiFi",
        "Connect": "Połącz",
        "Bluetooth": "Bluetooth",
        
        # Common buttons
        "ON": "WŁ",
        "OFF": "WYŁ",
        "OK": "OK",
        "CANCEL": "ANULUJ",
        "SPACE": "SPACJA",
        "CLR": "WYCZYŚĆ"
    },
    
    "Deutsch": {
        # Main navigation
        "HOME": "STARTSEITE",
        "SETTINGS": "EINSTELLUNGEN",
        "CLIMATE": "KLIMA",
        "LIGHTNING": "BELEUCHTUNG",
        "BATTERY": "BATTERIE",
        "ALARM": "ALARM",
        "ENGINE": "MOTOR",
        "WATER": "WASSER",
        "FUEL": "KRAFTSTOFF",
        "AUTOPILOT": "AUTOPILOT",
        
        # Settings categories
        "DISPLAY": "ANZEIGE",
        "LANGUAGE": "SPRACHE",
        "SYSTEM": "SYSTEM",
        "SAFETY": "SICHERHEIT",
        "CONNECTION": "VERBINDUNG",
        "CALIBRATION": "KALIBRIERUNG",
        
        # Display settings
        "Brightness": "Helligkeit",
        "Theme Mode": "Design-Modus",
        "Screen Orientation": "Bildschirmausrichtung",
        "Day": "Tag",
        "Night": "Nacht",
        "Landscape": "Querformat",
        "Portrait": "Hochformat",
        
        # Language settings
        "Language": "Sprache",
        "Time Format": "Zeitformat",
        "Units": "Einheiten",
        "Metric": "Metrisch",
        "Imperial": "Imperial",
        
        # System settings
        "Firmware Version": "Firmware-Version",
        "Update Firmware": "Firmware aktualisieren",
        "Restart System": "System neu starten",
        "Factory Reset": "Werkseinstellungen",
        
        # Safety settings
        "PIN Code Protection": "PIN-Code-Schutz",
        "Enter 4-digit PIN": "4-stellige PIN eingeben",
        "Set PIN": "PIN setzen",
        "PIN Protection": "PIN-Schutz",
        "Emergency Contact/Frequency": "Notfallkontakt/Frequenz",
        "Secure Mode": "Sicherer Modus",
        
        # Connection settings
        "WiFi": "WiFi",
        "Scan Networks": "Netzwerke scannen",
        "Connected to:": "Verbunden mit:",
        "Not connected": "Nicht verbunden",
        "Available Networks:": "Verfügbare Netzwerke:",
        "No networks found. Press \"Scan Networks\" to search.": "Keine Netzwerke gefunden. Drücken Sie \"Netzwerke scannen\" zum Suchen.",
        "Password:": "Passwort:",
        "Enter WiFi password": "WiFi-Passwort eingeben",
        "Connect": "Verbinden",
        "Bluetooth": "Bluetooth",
        
        # Common buttons
        "ON": "EIN",
        "OFF": "AUS",
        "OK": "OK",
        "CANCEL": "ABBRECHEN",
        "SPACE": "LEERZEICHEN",
        "CLR": "LÖSCHEN"
    },
    
    "Русский": {
        # Main navigation
        "HOME": "ГЛАВНАЯ",
        "SETTINGS": "НАСТРОЙКИ",
        "CLIMATE": "КЛИМАТ",
        "LIGHTNING": "ОСВЕЩЕНИЕ",
        "BATTERY": "БАТАРЕЯ",
        "ALARM": "ТРЕВОГА",
        "ENGINE": "ДВИГАТЕЛЬ",
        "WATER": "ВОДА",
        "FUEL": "ТОПЛИВО",
        "AUTOPILOT": "АВТОПИЛОТ",
        
        # Settings categories
        "DISPLAY": "ДИСПЛЕЙ",
        "LANGUAGE": "ЯЗЫК",
        "SYSTEM": "СИСТЕМА",
        "SAFETY": "БЕЗОПАСНОСТЬ",
        "CONNECTION": "СОЕДИНЕНИЕ",
        "CALIBRATION": "КАЛИБРОВКА",
        
        # Display settings
        "Brightness": "Яркость",
        "Theme Mode": "Режим темы",
        "Screen Orientation": "Ориентация экрана",
        "Day": "День",
        "Night": "Ночь",
        "Landscape": "Альбомная",
        "Portrait": "Книжная",
        
        # Language settings
        "Language": "Язык",
        "Time Format": "Формат времени",
        "Units": "Единицы",
        "Metric": "Метрические",
        "Imperial": "Имперские",
        
        # System settings
        "Firmware Version": "Версия прошивки",
        "Update Firmware": "Обновить прошивку",
        "Restart System": "Перезагрузить систему",
        "Factory Reset": "Сброс к заводским",
        
        # Safety settings
        "PIN Code Protection": "Защита PIN-кодом",
        "Enter 4-digit PIN": "Введите 4-значный PIN",
        "Set PIN": "Установить PIN",
        "PIN Protection": "Защита PIN",
        "Emergency Contact/Frequency": "Аварийный контакт/Частота",
        "Secure Mode": "Безопасный режим",
        
        # Connection settings
        "WiFi": "WiFi",
        "Scan Networks": "Сканировать сети",
        "Connected to:": "Подключено к:",
        "Not connected": "Не подключено",
        "Available Networks:": "Доступные сети:",
        "No networks found. Press \"Scan Networks\" to search.": "Сети не найдены. Нажмите \"Сканировать сети\" для поиска.",
        "Password:": "Пароль:",
        "Enter WiFi password": "Введите пароль WiFi",
        "Connect": "Подключить",
        "Bluetooth": "Bluetooth",
        
        # Common buttons
        "ON": "ВКЛ",
        "OFF": "ВЫКЛ",
        "OK": "ОК",
        "CANCEL": "ОТМЕНА",
        "SPACE": "ПРОБЕЛ",
        "CLR": "ОЧИСТИТЬ"
    }
}

def get_translation(key, language="English"):
    """
    Get translation for a key in specified language
    
    Args:
        key (str): Translation key
        language (str): Target language
        
    Returns:
        str: Translated text or original key if not found
    """
    if language in TRANSLATIONS and key in TRANSLATIONS[language]:
        return TRANSLATIONS[language][key]
    elif key in TRANSLATIONS["English"]:
        return TRANSLATIONS["English"][key]  # Fallback to English
    else:
        return key  # Return original key if no translation found

def get_available_languages():
    """Get list of available languages"""
    return list(TRANSLATIONS.keys())

def translate_dict(data_dict, language="English"):
    """
    Translate all values in a dictionary
    
    Args:
        data_dict (dict): Dictionary with keys to translate
        language (str): Target language
        
    Returns:
        dict: Dictionary with translated values
    """
    translated = {}
    for key, value in data_dict.items():
        if isinstance(value, str):
            translated[key] = get_translation(value, language)
        else:
            translated[key] = value
    return translated
