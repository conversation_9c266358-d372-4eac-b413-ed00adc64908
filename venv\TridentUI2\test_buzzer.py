#!/usr/bin/env python3
"""
Test script for TridentOS buzzer controller functionality.

This script tests the buzzer controller and alarm system integration
to ensure the critical alarm buzzer system works correctly.
"""

import time
import sys
import os

# Add the current directory to Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_buzzer_controller():
    """Test the buzzer controller directly."""
    print("=== TESTING BUZZER CONTROLLER ===")
    
    try:
        from buzzer_controller import get_buzzer_controller
        
        buzzer = get_buzzer_controller()
        if not buzzer:
            print("❌ Buzzer controller not available")
            return False
            
        print("✅ Buzzer controller initialized")
        
        # Test activation
        print("🔔 Activating buzzer for 5 seconds...")
        buzzer.activate("critical", "Test critical alarm")
        
        if buzzer.is_buzzer_active():
            print("✅ Buzzer activated successfully")
        else:
            print("❌ Buzzer activation failed")
            return False
            
        # Let it beep for 5 seconds
        time.sleep(5)
        
        # Test deactivation
        print("🔇 Deactivating buzzer...")
        buzzer.deactivate("Test completed")
        
        if not buzzer.is_buzzer_active():
            print("✅ Buzzer deactivated successfully")
        else:
            print("❌ Buzzer deactivation failed")
            return False
            
        # Show stats
        stats = buzzer.get_stats()
        print(f"📊 Buzzer stats: {stats}")
        
        return True
        
    except Exception as e:
        print(f"❌ Buzzer controller test failed: {e}")
        return False

def test_alarm_integration():
    """Test the alarm manager integration with buzzer."""
    print("\n=== TESTING ALARM INTEGRATION ===")
    
    try:
        import alarm_manager
        
        # Get alarm manager instance
        alarm_mgr = alarm_manager.get_instance()
        print("✅ Alarm manager initialized")
        
        # Add a critical alarm (should activate buzzer)
        print("🚨 Adding critical test alarm...")
        alarm_id = alarm_mgr.add_alarm(
            alarm_manager.ALARM_CRITICAL,
            "TEST: Critical buzzer test alarm",
            alarm_manager.SOURCE_SYSTEM
        )
        
        if alarm_id:
            print(f"✅ Critical alarm added: {alarm_id}")
        else:
            print("❌ Failed to add critical alarm")
            return False
            
        # Check buzzer status
        buzzer_status = alarm_mgr.get_buzzer_status()
        print(f"🔔 Buzzer status: {buzzer_status}")
        
        # Let it beep for 3 seconds
        print("⏱️  Letting buzzer beep for 3 seconds...")
        time.sleep(3)
        
        # Acknowledge the critical alarm (should deactivate buzzer)
        print("✅ Acknowledging critical alarm...")
        acknowledged_count = alarm_mgr.acknowledge_all_critical_alarms("Test Operator")
        
        if acknowledged_count > 0:
            print(f"✅ Acknowledged {acknowledged_count} critical alarms")
        else:
            print("❌ Failed to acknowledge critical alarms")
            return False
            
        # Check buzzer status again
        buzzer_status = alarm_mgr.get_buzzer_status()
        print(f"🔇 Buzzer status after acknowledgment: {buzzer_status}")
        
        # Clean up - deactivate the test alarm
        alarm_mgr.deactivate_alarm(alarm_id, "Test completed", "Test System")
        print("🧹 Test alarm cleaned up")
        
        return True
        
    except Exception as e:
        print(f"❌ Alarm integration test failed: {e}")
        return False

def test_gpio_availability():
    """Test GPIO library availability."""
    print("\n=== TESTING GPIO AVAILABILITY ===")

    # Test improved platform detection
    from buzzer_controller import detect_raspberry_pi
    is_rpi = detect_raspberry_pi()

    import platform
    print(f"🖥️  Platform: {platform.machine()}")
    print(f"🖥️  Node: {platform.node()}")
    print(f"🖥️  Platform info: {platform.platform()}")
    print(f"🍓 Raspberry Pi detected (improved): {is_rpi}")

    if not is_rpi:
        print("ℹ️  Not running on Raspberry Pi - GPIO functionality will be simulated")
        return True
    
    # Test GPIO libraries
    gpio_available = False
    
    try:
        import gpiozero
        print(f"✅ GPIO Zero available: {gpiozero.__version__}")
        gpio_available = True
    except ImportError:
        print("❌ GPIO Zero not available")
    
    try:
        import RPi.GPIO
        print(f"✅ RPi.GPIO available: {RPi.GPIO.VERSION}")
        gpio_available = True
    except ImportError:
        print("❌ RPi.GPIO not available")
    
    try:
        import pigpio
        print(f"✅ pigpio available")
        gpio_available = True
    except ImportError:
        print("❌ pigpio not available")
    
    return gpio_available

def main():
    """Run all tests."""
    print("🔔 TridentOS Buzzer System Test")
    print("=" * 50)
    
    # Test GPIO availability
    gpio_ok = test_gpio_availability()
    
    # Test buzzer controller
    buzzer_ok = test_buzzer_controller()
    
    # Test alarm integration
    alarm_ok = test_alarm_integration()
    
    # Summary
    print("\n" + "=" * 50)
    print("📋 TEST SUMMARY")
    print("=" * 50)
    print(f"🖥️  GPIO Libraries: {'✅ PASS' if gpio_ok else '❌ FAIL'}")
    print(f"🔔 Buzzer Controller: {'✅ PASS' if buzzer_ok else '❌ FAIL'}")
    print(f"🚨 Alarm Integration: {'✅ PASS' if alarm_ok else '❌ FAIL'}")
    
    overall_result = gpio_ok and buzzer_ok and alarm_ok
    print(f"\n🎯 OVERALL RESULT: {'✅ ALL TESTS PASSED' if overall_result else '❌ SOME TESTS FAILED'}")
    
    if not overall_result:
        print("\n💡 TROUBLESHOOTING TIPS:")
        if not gpio_ok:
            print("   - Install GPIO libraries: pip install gpiozero pigpio RPi.GPIO")
            print("   - Enable pigpio daemon: sudo systemctl enable pigpiod")
            print("   - Add user to gpio group: sudo usermod -a -G gpio $USER")
        if not buzzer_ok:
            print("   - Check GPIO PIN 5 connection")
            print("   - Verify buzzer hardware")
        if not alarm_ok:
            print("   - Check alarm manager configuration")
            print("   - Verify state manager functionality")
    
    return overall_result

if __name__ == "__main__":
    try:
        success = main()
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        print("\n\n⏹️  Test interrupted by user")
        sys.exit(1)
    except Exception as e:
        print(f"\n\n💥 Unexpected error: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)
