#!/usr/bin/env python3
"""
Quick Raspberry Pi detection test for TridentOS.
"""

import sys
import os

# Add current directory to path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def main():
    print("🔍 Quick Raspberry Pi Detection Test")
    print("=" * 40)
    
    try:
        # Test the improved detection
        from buzzer_controller import detect_raspberry_pi, IS_RASPBERRY_PI
        
        print("Testing improved Raspberry Pi detection...")
        is_rpi = detect_raspberry_pi()
        
        print(f"Detection result: {is_rpi}")
        print(f"Global IS_RASPBERRY_PI: {IS_RASPBERRY_PI}")
        
        if is_rpi:
            print("✅ Raspberry Pi detected!")
            
            # Test GPIO libraries
            print("\nTesting GPIO libraries...")
            
            try:
                import gpiozero
                print(f"✅ gpiozero {gpiozero.__version__} available")
            except ImportError as e:
                print(f"❌ gpiozero not available: {e}")
            
            try:
                import RPi.GPIO
                print(f"✅ RPi.GPIO {RPi.GPIO.VERSION} available")
            except ImportError as e:
                print(f"❌ RPi.GPIO not available: {e}")
            
            try:
                import pigpio
                print("✅ pigpio available")
            except ImportError as e:
                print(f"❌ pigpio not available: {e}")
            
            # Test buzzer controller
            print("\nTesting buzzer controller...")
            try:
                from buzzer_controller import get_buzzer_controller
                buzzer = get_buzzer_controller()
                
                if buzzer:
                    print(f"✅ Buzzer controller created")
                    print(f"   GPIO library: {getattr(buzzer, 'gpio_library', 'unknown')}")
                    print(f"   GPIO available: {getattr(buzzer, 'buzzer', None) is not None}")
                else:
                    print("❌ Buzzer controller creation failed")
                    
            except Exception as e:
                print(f"❌ Buzzer controller test failed: {e}")
        else:
            print("❌ Raspberry Pi not detected")
            print("This may be normal if running on PC/Windows")
            
    except Exception as e:
        print(f"❌ Test failed: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
