# TridentOS Makefile for Raspberry Pi 5
# Provides easy installation and management commands

.PHONY: help install setup test run clean uninstall status logs backup restore

# Default target
help:
	@echo "TridentOS Marine Control System - Makefile"
	@echo "=========================================="
	@echo ""
	@echo "Available commands:"
	@echo "  make install    - Install TridentOS system-wide"
	@echo "  make setup      - Setup environment and dependencies"
	@echo "  make test       - Test configuration"
	@echo "  make run        - Run TridentOS"
	@echo "  make service    - Install and start systemd service"
	@echo "  make desktop    - Install desktop entry"
	@echo "  make status     - Show service status"
	@echo "  make logs       - Show recent logs"
	@echo "  make backup     - Create backup"
	@echo "  make restore    - Restore from backup"
	@echo "  make clean      - Clean temporary files"
	@echo "  make uninstall  - Uninstall TridentOS"
	@echo ""

# Setup environment and dependencies
setup:
	@echo "Setting up TridentOS environment..."
	chmod +x start_tridentos.sh
	chmod +x start_tridentos.py
	./start_tridentos.sh --setup
	@echo "✓ Setup completed"

# Test configuration
test:
	@echo "Testing TridentOS configuration..."
	./start_tridentos.sh --test
	python3 test_enhanced_settings.py
	python3 test_settings_integration.py
	@echo "✓ All tests completed"

# Run TridentOS
run:
	@echo "Starting TridentOS..."
	./start_tridentos.sh

# Install desktop entry
desktop:
	@echo "Installing desktop entry..."
	mkdir -p ~/.local/share/applications
	sed 's|%k|$(PWD)|g' TridentOS.desktop > ~/.local/share/applications/TridentOS.desktop
	chmod +x ~/.local/share/applications/TridentOS.desktop
	update-desktop-database ~/.local/share/applications/ 2>/dev/null || true
	@echo "✓ Desktop entry installed"

# Install systemd service
service: setup
	@echo "Installing systemd service..."
	sudo cp tridentos.service /etc/systemd/system/
	sudo sed -i 's|/home/<USER>/TridentOS/venv/TridentUI2|$(PWD)|g' /etc/systemd/system/tridentos.service
	sudo sed -i 's|User=pi|User=$(USER)|g' /etc/systemd/system/tridentos.service
	sudo sed -i 's|Group=pi|Group=$(USER)|g' /etc/systemd/system/tridentos.service
	sudo sed -i 's|/home/<USER>/TridentOS|$(dir $(PWD))|g' /etc/systemd/system/tridentos.service
	sudo systemctl daemon-reload
	sudo systemctl enable tridentos.service
	@echo "✓ Systemd service installed and enabled"
	@echo "  Start with: sudo systemctl start tridentos"
	@echo "  Stop with:  sudo systemctl stop tridentos"

# Full installation
install: setup desktop service
	@echo "TridentOS installation completed!"
	@echo ""
	@echo "You can now:"
	@echo "  1. Run from desktop: Click TridentOS icon"
	@echo "  2. Run from terminal: make run"
	@echo "  3. Start service: sudo systemctl start tridentos"
	@echo "  4. Auto-start on boot: Service is already enabled"

# Show service status
status:
	@echo "TridentOS Service Status:"
	@echo "========================"
	systemctl is-active tridentos.service || echo "Service not running"
	systemctl is-enabled tridentos.service || echo "Service not enabled"
	@echo ""
	@echo "Recent service logs:"
	journalctl -u tridentos.service --no-pager -n 10 || echo "No service logs available"

# Show logs
logs:
	@echo "TridentOS Logs:"
	@echo "==============="
	@echo "Application logs:"
	ls -la logs/ 2>/dev/null || echo "No application logs found"
	@echo ""
	@echo "Service logs:"
	journalctl -u tridentos.service --no-pager -n 20 || echo "No service logs available"

# Create backup
backup:
	@echo "Creating TridentOS backup..."
	mkdir -p backups
	tar -czf backups/tridentos_backup_$(shell date +%Y%m%d_%H%M%S).tar.gz \
		--exclude='backups' \
		--exclude='logs' \
		--exclude='__pycache__' \
		--exclude='*.pyc' \
		.
	@echo "✓ Backup created in backups/ directory"

# Restore from backup
restore:
	@echo "Available backups:"
	@ls -la backups/tridentos_backup_*.tar.gz 2>/dev/null || echo "No backups found"
	@echo ""
	@echo "To restore, run: tar -xzf backups/tridentos_backup_YYYYMMDD_HHMMSS.tar.gz"

# Clean temporary files
clean:
	@echo "Cleaning temporary files..."
	find . -name "*.pyc" -delete
	find . -name "__pycache__" -type d -exec rm -rf {} + 2>/dev/null || true
	rm -f *.log
	@echo "✓ Cleanup completed"

# Uninstall TridentOS
uninstall:
	@echo "Uninstalling TridentOS..."
	sudo systemctl stop tridentos.service 2>/dev/null || true
	sudo systemctl disable tridentos.service 2>/dev/null || true
	sudo rm -f /etc/systemd/system/tridentos.service
	sudo systemctl daemon-reload
	rm -f ~/.local/share/applications/TridentOS.desktop
	update-desktop-database ~/.local/share/applications/ 2>/dev/null || true
	@echo "✓ TridentOS uninstalled"
	@echo "Note: Application files remain in $(PWD)"

# Quick start for development
dev: setup test
	@echo "Development environment ready"
	@echo "Run 'make run' to start TridentOS"

# Install for production
prod: install
	sudo systemctl start tridentos.service
	@echo "TridentOS started in production mode"

# Emergency stop
stop:
	@echo "Stopping TridentOS..."
	sudo systemctl stop tridentos.service 2>/dev/null || true
	pkill -f "python3 main.py" 2>/dev/null || true
	pkill -f "start_tridentos" 2>/dev/null || true
	@echo "✓ TridentOS stopped"

# Restart service
restart:
	@echo "Restarting TridentOS service..."
	sudo systemctl restart tridentos.service
	@echo "✓ TridentOS service restarted"

# Show system information
info:
	@echo "TridentOS System Information:"
	@echo "============================="
	@echo "Platform: $(shell uname -a)"
	@echo "Python: $(shell python3 --version)"
	@echo "Working directory: $(PWD)"
	@echo "User: $(USER)"
	@echo ""
	@echo "Raspberry Pi detection:"
	@if [ -f /proc/cpuinfo ]; then \
		if grep -q "BCM" /proc/cpuinfo && grep -q "ARM" /proc/cpuinfo; then \
			echo "✓ Running on Raspberry Pi"; \
		else \
			echo "⚠ Not running on Raspberry Pi"; \
		fi \
	else \
		echo "⚠ Cannot detect platform"; \
	fi
	@echo ""
	@echo "Display: $(DISPLAY)"
	@echo ""
	@echo "Required files:"
	@for file in main.py translations.py ui/settings.kv widgets/virtual_keyboard.py; do \
		if [ -f "$$file" ]; then \
			echo "✓ $$file"; \
		else \
			echo "✗ $$file (missing)"; \
		fi \
	done
