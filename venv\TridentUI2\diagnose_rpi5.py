#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Enhanced Raspberry Pi 5 Detection and Diagnostics for TridentOS
Comprehensive detection and troubleshooting for RPi5 compatibility
"""

import os
import sys
import platform
import subprocess
import json
from pathlib import Path

def print_header(title):
    """Print formatted header"""
    print(f"\n{'='*60}")
    print(f" {title}")
    print(f"{'='*60}")

def print_section(title):
    """Print formatted section"""
    print(f"\n--- {title} ---")

def check_basic_system_info():
    """Check basic system information"""
    print_section("Basic System Information")
    
    try:
        print(f"Platform: {platform.platform()}")
        print(f"Machine: {platform.machine()}")
        print(f"Processor: {platform.processor()}")
        print(f"Architecture: {platform.architecture()}")
        print(f"Node: {platform.node()}")
        print(f"System: {platform.system()}")
        print(f"Release: {platform.release()}")
        print(f"Version: {platform.version()}")
    except Exception as e:
        print(f"Error getting system info: {e}")

def enhanced_rpi_detection():
    """Enhanced Raspberry Pi detection with detailed output"""
    print_section("Enhanced Raspberry Pi Detection")
    
    detection_results = {}
    
    # Method 1: Check /proc/cpuinfo
    try:
        with open('/proc/cpuinfo', 'r') as f:
            cpuinfo = f.read()
            
        if 'Raspberry Pi' in cpuinfo:
            print("✅ Method 1: /proc/cpuinfo contains 'Raspberry Pi'")
            detection_results['cpuinfo_rpi'] = True
            
            # Extract model info
            for line in cpuinfo.split('\n'):
                if 'Model' in line:
                    model = line.split(':')[1].strip()
                    print(f"   Model: {model}")
                    detection_results['model'] = model
                elif 'Hardware' in line:
                    hardware = line.split(':')[1].strip()
                    print(f"   Hardware: {hardware}")
                    detection_results['hardware'] = hardware
                elif 'Revision' in line:
                    revision = line.split(':')[1].strip()
                    print(f"   Revision: {revision}")
                    detection_results['revision'] = revision
        else:
            print("❌ Method 1: /proc/cpuinfo does not contain 'Raspberry Pi'")
            detection_results['cpuinfo_rpi'] = False
        
        # Check for BCM chips
        bcm_chips = ['BCM2835', 'BCM2711', 'BCM2712']
        found_chips = [chip for chip in bcm_chips if chip in cpuinfo]
        if found_chips:
            print(f"✅ Method 1b: Found BCM chips: {found_chips}")
            detection_results['bcm_chips'] = found_chips
            if 'BCM2712' in found_chips:
                print("   🎉 BCM2712 detected - This is likely Raspberry Pi 5!")
        else:
            print("❌ Method 1b: No BCM chips found")
            detection_results['bcm_chips'] = []
            
    except FileNotFoundError:
        print("❌ Method 1: /proc/cpuinfo not found")
        detection_results['cpuinfo_rpi'] = False
    except Exception as e:
        print(f"❌ Method 1: Error reading /proc/cpuinfo: {e}")
        detection_results['cpuinfo_rpi'] = False
    
    # Method 2: Check device tree model
    try:
        with open('/proc/device-tree/model', 'r') as f:
            model = f.read().strip('\x00')
        print(f"✅ Method 2: Device tree model: '{model}'")
        detection_results['device_tree_model'] = model
        if 'Raspberry Pi' in model:
            print("   ✅ Device tree confirms Raspberry Pi")
            detection_results['device_tree_rpi'] = True
        else:
            print("   ❌ Device tree does not show Raspberry Pi")
            detection_results['device_tree_rpi'] = False
    except FileNotFoundError:
        print("❌ Method 2: /proc/device-tree/model not found")
        detection_results['device_tree_rpi'] = False
    except Exception as e:
        print(f"❌ Method 2: Error reading device tree: {e}")
        detection_results['device_tree_rpi'] = False
    
    # Method 3: Check architecture
    machine = platform.machine().lower()
    print(f"✅ Method 3: Machine architecture: {machine}")
    detection_results['architecture'] = machine
    if machine.startswith('arm') or machine.startswith('aarch'):
        print("   ✅ ARM architecture detected")
        detection_results['is_arm'] = True
    else:
        print("   ❌ Not ARM architecture")
        detection_results['is_arm'] = False
    
    # Method 4: Check hostname
    hostname = platform.node().lower()
    print(f"✅ Method 4: Hostname: {hostname}")
    detection_results['hostname'] = hostname
    if 'raspberry' in hostname or 'rpi' in hostname:
        print("   ✅ Hostname suggests Raspberry Pi")
        detection_results['hostname_rpi'] = True
    else:
        print("   ❌ Hostname does not suggest Raspberry Pi")
        detection_results['hostname_rpi'] = False
    
    # Method 5: Check GPIO paths
    gpio_paths = ['/sys/class/gpio', '/dev/gpiomem', '/dev/gpiochip0', '/dev/gpiochip4']
    gpio_results = {}
    for path in gpio_paths:
        exists = os.path.exists(path)
        gpio_results[path] = exists
        status = "✅" if exists else "❌"
        print(f"   {status} GPIO path: {path}")
    
    detection_results['gpio_paths'] = gpio_results
    gpio_available = any(gpio_results.values())
    print(f"✅ Method 5: GPIO availability: {gpio_available}")
    
    # Final determination
    confidence_score = 0
    if detection_results.get('cpuinfo_rpi', False):
        confidence_score += 40
    if detection_results.get('device_tree_rpi', False):
        confidence_score += 40
    if detection_results.get('bcm_chips') and 'BCM2712' in detection_results['bcm_chips']:
        confidence_score += 30  # High confidence for RPi5
    elif detection_results.get('bcm_chips'):
        confidence_score += 20
    if detection_results.get('is_arm', False):
        confidence_score += 10
    if detection_results.get('hostname_rpi', False):
        confidence_score += 5
    if gpio_available:
        confidence_score += 5
    
    print(f"\n🎯 Detection confidence: {confidence_score}%")
    
    if confidence_score >= 70:
        print("🎉 HIGH CONFIDENCE: This is a Raspberry Pi!")
        if 'BCM2712' in detection_results.get('bcm_chips', []):
            print("🚀 RASPBERRY PI 5 DETECTED!")
    elif confidence_score >= 40:
        print("⚠️  MEDIUM CONFIDENCE: Likely a Raspberry Pi")
    else:
        print("❌ LOW CONFIDENCE: Probably not a Raspberry Pi")
    
    return detection_results, confidence_score

def check_gpio_functionality():
    """Check GPIO functionality"""
    print_section("GPIO Functionality Check")
    
    # Check GPIO groups
    try:
        import grp
        try:
            gpio_group = grp.getgrnam('gpio')
            current_user = os.getenv('USER', 'unknown')
            if current_user in gpio_group.gr_mem:
                print(f"✅ User '{current_user}' is in gpio group")
            else:
                print(f"❌ User '{current_user}' is NOT in gpio group")
                print("   Fix: sudo usermod -a -G gpio $USER")
        except KeyError:
            print("❌ GPIO group not found")
    except ImportError:
        print("❌ Cannot check groups (grp module not available)")
    
    # Test GPIO libraries
    gpio_libs = ['RPi.GPIO', 'gpiozero', 'pigpio']
    for lib in gpio_libs:
        try:
            __import__(lib)
            print(f"✅ {lib} library available")
        except ImportError:
            print(f"❌ {lib} library not available")
            print(f"   Fix: pip install {lib}")

def check_system_capabilities():
    """Check system capabilities for TridentOS"""
    print_section("System Capabilities for TridentOS")
    
    # Check brightness control
    brightness_path = '/sys/class/backlight/rpi_backlight'
    if os.path.exists(brightness_path):
        print("✅ Brightness control available")
        try:
            with open(f'{brightness_path}/max_brightness', 'r') as f:
                max_brightness = f.read().strip()
            print(f"   Max brightness: {max_brightness}")
            
            # Test write permission
            try:
                with open(f'{brightness_path}/brightness', 'r') as f:
                    current = f.read().strip()
                print(f"   Current brightness: {current}")
                print("   ✅ Brightness readable")
            except PermissionError:
                print("   ❌ Cannot read brightness (permission denied)")
        except Exception as e:
            print(f"   ❌ Error checking brightness: {e}")
    else:
        print("❌ Brightness control not available")
    
    # Check WiFi
    try:
        result = subprocess.run(['iwconfig'], capture_output=True, text=True, timeout=5)
        if 'wlan0' in result.stdout:
            print("✅ WiFi interface wlan0 available")
        else:
            print("❌ WiFi interface wlan0 not found")
    except FileNotFoundError:
        print("❌ iwconfig command not found")
        print("   Fix: sudo apt install wireless-tools")
    except subprocess.TimeoutExpired:
        print("❌ iwconfig command timed out")
    except Exception as e:
        print(f"❌ Error checking WiFi: {e}")
    
    # Check audio
    audio_devices = ['/dev/snd', '/proc/asound']
    for device in audio_devices:
        if os.path.exists(device):
            print(f"✅ Audio device available: {device}")
        else:
            print(f"❌ Audio device not found: {device}")

def check_python_environment():
    """Check Python environment for TridentOS"""
    print_section("Python Environment Check")
    
    print(f"Python version: {sys.version}")
    print(f"Python executable: {sys.executable}")
    
    # Check required packages
    required_packages = [
        'kivy', 'kivymd', 'RPi.GPIO', 'gpiozero', 
        'psutil', 'json', 'os', 'platform'
    ]
    
    for package in required_packages:
        try:
            __import__(package)
            print(f"✅ {package} available")
        except ImportError:
            print(f"❌ {package} not available")
            if package in ['RPi.GPIO', 'gpiozero']:
                print(f"   Note: {package} is only needed on Raspberry Pi")
            else:
                print(f"   Fix: pip install {package}")

def generate_fix_script(detection_results, confidence_score):
    """Generate a fix script based on detection results"""
    print_section("Generating Fix Script")
    
    fixes = []
    
    if confidence_score < 70:
        fixes.append("# Force Raspberry Pi detection in TridentOS")
        fixes.append("# Edit main.py and set IS_RASPBERRY_PI = True")
    
    if not detection_results.get('gpio_paths', {}).get('/sys/class/gpio', False):
        fixes.append("# Enable GPIO")
        fixes.append("sudo modprobe gpio")
    
    # Check if user needs to be added to groups
    try:
        import grp
        current_user = os.getenv('USER', 'pi')
        groups_to_add = []
        
        for group_name in ['gpio', 'dialout', 'video', 'audio']:
            try:
                group = grp.getgrnam(group_name)
                if current_user not in group.gr_mem:
                    groups_to_add.append(group_name)
            except KeyError:
                pass
        
        if groups_to_add:
            fixes.append(f"# Add user to required groups")
            for group in groups_to_add:
                fixes.append(f"sudo usermod -a -G {group} {current_user}")
            fixes.append("# Logout and login again after adding to groups")
    except:
        pass
    
    if fixes:
        fix_script_path = 'fix_rpi5_detection.sh'
        with open(fix_script_path, 'w') as f:
            f.write("#!/bin/bash\n")
            f.write("# Auto-generated fix script for Raspberry Pi 5 detection\n\n")
            for fix in fixes:
                f.write(fix + "\n")
        
        os.chmod(fix_script_path, 0o755)
        print(f"✅ Fix script generated: {fix_script_path}")
        print("   Run with: ./fix_rpi5_detection.sh")
    else:
        print("✅ No fixes needed - system looks good!")

def main():
    """Main diagnostic function"""
    print_header("TridentOS Raspberry Pi 5 Diagnostics")
    
    check_basic_system_info()
    detection_results, confidence_score = enhanced_rpi_detection()
    check_gpio_functionality()
    check_system_capabilities()
    check_python_environment()
    generate_fix_script(detection_results, confidence_score)
    
    print_header("Diagnostic Summary")
    print(f"Raspberry Pi detection confidence: {confidence_score}%")
    
    if confidence_score >= 70:
        print("🎉 System appears to be a Raspberry Pi and should work with TridentOS!")
        if 'BCM2712' in detection_results.get('bcm_chips', []):
            print("🚀 Raspberry Pi 5 specifically detected!")
    elif confidence_score >= 40:
        print("⚠️  System might be a Raspberry Pi but needs verification")
        print("   Check the generated fix script for potential solutions")
    else:
        print("❌ System does not appear to be a Raspberry Pi")
        print("   TridentOS will run in simulation mode")
    
    print("\nNext steps:")
    print("1. If this is a Raspberry Pi but not detected, run the fix script")
    print("2. Test TridentOS with: python3 start_tridentos.py --test")
    print("3. Run full application with: ./start_tridentos.sh")

if __name__ == "__main__":
    main()
