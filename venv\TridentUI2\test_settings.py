#!/usr/bin/env python3
"""
Simple test script to verify the Settings screen functionality
"""

import sys
import os

# Add the current directory to the path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

try:
    from kivy.app import App
    from kivy.lang import Builder
    from kivy.uix.screenmanager import ScreenManager, Screen
    from kivy.properties import StringProperty, NumericProperty, BooleanProperty
    from kivy.uix.label import Label
    from kivy.clock import Clock
    from datetime import datetime
    
    # Simple ClockWidget for testing
    class ClockWidget(Label):
        def __init__(self, **kwargs):
            super(ClockWidget, self).__init__(**kwargs)
            self.update_time(None)
            Clock.schedule_interval(self.update_time, 1)
        
        def update_time(self, dt):
            self.text = datetime.now().strftime("%H:%M")
    
    # Register ClockWidget
    from kivy.factory import Factory
    Factory.register('ClockWidget', ClockWidget)
    
    # Simple SettingsScreen for testing
    class SettingsScreen(Screen):
        selected_category = StringProperty("DISPLAY")
        brightness = NumericProperty(80)
        theme_mode = StringProperty("Night")
        screen_orientation = StringProperty("Landscape")
        language = StringProperty("English")
        time_format = StringProperty("24h")
        units = StringProperty("Metric")
        firmware_version = StringProperty("TridentOS v2.1.0")
        settings_lock_enabled = BooleanProperty(False)
        emergency_contact = StringProperty("VHF Channel 16")
        secure_mode = BooleanProperty(False)
        wifi_enabled = BooleanProperty(True)
        bluetooth_enabled = BooleanProperty(True)
        nmea_id = StringProperty("001")
        water_sensor_zero = NumericProperty(0)
        fuel_sensor_zero = NumericProperty(0)
        
        def select_category(self, category):
            self.selected_category = category
            print(f"Selected category: {category}")
        
        def set_brightness(self, value):
            self.brightness = value
            print(f"Brightness set to: {value}")
        
        def toggle_theme(self):
            self.theme_mode = "Day" if self.theme_mode == "Night" else "Night"
            print(f"Theme changed to: {self.theme_mode}")
        
        def set_orientation(self, orientation):
            self.screen_orientation = orientation
            print(f"Orientation set to: {orientation}")
        
        def set_language(self, language):
            self.language = language
            print(f"Language set to: {language}")
        
        def toggle_time_format(self):
            self.time_format = "12h" if self.time_format == "24h" else "24h"
            print(f"Time format changed to: {self.time_format}")
        
        def toggle_units(self):
            self.units = "Imperial" if self.units == "Metric" else "Metric"
            print(f"Units changed to: {self.units}")
        
        def update_firmware(self):
            print("Firmware update initiated...")
        
        def restart_system(self):
            print("System restart initiated...")
        
        def factory_reset(self):
            print("Factory reset initiated...")
        
        def toggle_settings_lock(self):
            self.settings_lock_enabled = not self.settings_lock_enabled
            print(f"Settings lock: {'ON' if self.settings_lock_enabled else 'OFF'}")
        
        def set_emergency_contact(self, contact):
            self.emergency_contact = contact
            print(f"Emergency contact set to: {contact}")
        
        def toggle_secure_mode(self):
            self.secure_mode = not self.secure_mode
            print(f"Secure mode: {'ON' if self.secure_mode else 'OFF'}")
        
        def toggle_wifi(self):
            self.wifi_enabled = not self.wifi_enabled
            print(f"WiFi: {'ON' if self.wifi_enabled else 'OFF'}")
        
        def toggle_bluetooth(self):
            self.bluetooth_enabled = not self.bluetooth_enabled
            print(f"Bluetooth: {'ON' if self.bluetooth_enabled else 'OFF'}")
        
        def set_nmea_id(self, nmea_id):
            self.nmea_id = nmea_id
            print(f"NMEA ID set to: {nmea_id}")
        
        def calibrate_compass(self):
            print("Compass calibration started...")
        
        def set_water_zero(self, value):
            self.water_sensor_zero = value
            print(f"Water sensor zero set to: {value}")
        
        def set_fuel_zero(self, value):
            self.fuel_sensor_zero = value
            print(f"Fuel sensor zero set to: {value}")
        
        def test_actuators(self):
            print("Testing actuators...")
        
        def go_to_home(self):
            print("Going to home screen...")
            App.get_running_app().stop()
    
    class TestApp(App):
        def build(self):
            # Load the settings UI
            Builder.load_file("ui/settings.kv")
            
            # Create screen manager
            sm = ScreenManager()
            sm.add_widget(SettingsScreen(name="settings"))
            sm.current = "settings"
            
            return sm
    
    if __name__ == "__main__":
        print("Starting Settings Screen Test...")
        TestApp().run()
        print("Test completed.")

except ImportError as e:
    print(f"Import error: {e}")
    print("Kivy is not installed or not available in this environment.")
    sys.exit(1)
except Exception as e:
    print(f"Error: {e}")
    sys.exit(1)
