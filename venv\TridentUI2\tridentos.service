[Unit]
Description=TridentOS Marine Control System
Documentation=https://github.com/tridentos/marine-control
After=graphical-session.target network.target
Wants=graphical-session.target network.target
Requires=graphical-session.target

[Service]
Type=simple
User=pi
Group=pi
Environment=DISPLAY=:0
Environment=HOME=/home/<USER>
Environment=XDG_RUNTIME_DIR=/run/user/1000
Environment=KIVY_WINDOW_FULLSCREEN=1
Environment=KIVY_WINDOW_BORDERLESS=1
Environment=KIVY_NO_CONSOLELOG=1
WorkingDirectory=/home/<USER>/TridentOS/venv/TridentUI2
ExecStartPre=/bin/sleep 10
ExecStartPre=/bin/bash -c "cd /home/<USER>/TridentOS/venv/TridentUI2 && ./start_tridentos.sh --setup"
ExecStart=/bin/bash -c "cd /home/<USER>/TridentOS/venv/TridentUI2 && python3 main.py"
ExecStop=/bin/kill -TERM $MAINPID
ExecStopPost=/bin/bash -c "pkill unclutter || true"
Restart=always
RestartSec=5
TimeoutStartSec=60
TimeoutStopSec=30
StandardOutput=journal
StandardError=journal
SyslogIdentifier=tridentos

# Security settings
NoNewPrivileges=true
PrivateTmp=true
ProtectSystem=strict
ProtectHome=false
ReadWritePaths=/home/<USER>/TridentOS
ReadOnlyPaths=/sys/class/backlight
SupplementaryGroups=gpio dialout video audio

# Resource limits
MemoryMax=1G
CPUQuota=80%

[Install]
WantedBy=graphical-session.target
Also=tridentos-watchdog.service
