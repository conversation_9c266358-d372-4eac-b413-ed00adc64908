import threading
import time
import logging
from functools import lru_cache

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger("BuzzerController")

# Platform detection for GPIO support
import platform
import os

def detect_raspberry_pi():
    """
    Improved Raspberry Pi detection using multiple methods.
    Returns True if running on Raspberry Pi, False otherwise.
    """
    detection_methods = []

    # Method 1: Check /proc/cpuinfo for Raspberry Pi
    try:
        with open('/proc/cpuinfo', 'r') as f:
            cpuinfo_content = f.read()
            if 'Raspberry Pi' in cpuinfo_content:
                detection_methods.append("cpuinfo")
                logger.info("Raspberry Pi detected via /proc/cpuinfo")
            # Also check for BCM chips used in Raspberry Pi
            if any(chip in cpuinfo_content for chip in ['BCM2835', 'BCM2711', 'BCM2712']):
                detection_methods.append("bcm-chip")
                logger.info("Raspberry Pi BCM chip detected")
    except (FileNotFoundError, PermissionError):
        pass

    # Method 2: Check device tree model
    try:
        with open('/proc/device-tree/model', 'r') as f:
            model = f.read().strip('\x00')
            if 'Raspberry Pi' in model:
                detection_methods.append("device-tree")
                logger.info(f"Raspberry Pi detected via device tree: {model}")
    except (FileNotFoundError, PermissionError):
        pass

    # Method 3: Check platform architecture
    machine = platform.machine()
    if machine.startswith('arm') or machine.startswith('aarch'):
        detection_methods.append("arm-architecture")
        logger.info(f"ARM architecture detected: {machine}")

    # Method 4: Check hostname
    node = platform.node().lower()
    if 'raspberry' in node or 'rpi' in node:
        detection_methods.append("hostname")
        logger.info(f"Raspberry Pi hostname detected: {node}")

    # Method 5: Check for GPIO hardware presence
    gpio_paths = ['/sys/class/gpio', '/dev/gpiomem', '/dev/gpiochip0']
    if any(os.path.exists(path) for path in gpio_paths):
        detection_methods.append("gpio-hardware")
        logger.info("GPIO hardware detected")

    is_rpi = len(detection_methods) > 0

    if is_rpi:
        logger.info(f"Raspberry Pi detected using methods: {detection_methods}")
    else:
        logger.info("Raspberry Pi not detected")
        logger.info(f"Platform info: machine={platform.machine()}, node={platform.node()}, platform={platform.platform()}")

    return is_rpi

IS_RASPBERRY_PI = detect_raspberry_pi()

# GPIO library imports with fallback
GPIO_AVAILABLE = False
if IS_RASPBERRY_PI:
    try:
        from gpiozero import Buzzer, Device
        from gpiozero.pins.pigpio import PiGPIOFactory
        GPIO_AVAILABLE = True
        logger.info("GPIO Zero library loaded successfully")
    except ImportError:
        try:
            import RPi.GPIO as GPIO
            GPIO_AVAILABLE = True
            logger.info("RPi.GPIO library loaded successfully")
        except ImportError:
            logger.warning("No GPIO library available - buzzer functionality disabled")
            GPIO_AVAILABLE = False
else:
    logger.info("Not running on Raspberry Pi - buzzer functionality disabled")

class BuzzerController:
    """
    Critical alarm buzzer controller for TridentOS marine control system.
    
    Features:
    - GPIO PIN 5 buzzer control
    - Continuous beeping patterns (500ms ON, 500ms OFF)
    - Background thread operation
    - Integration with alarm system
    - Error handling and safety features
    - Debounce protection
    """
    
    def __init__(self, pin=5, beep_on_duration=0.5, beep_off_duration=0.5):
        """
        Initialize the buzzer controller.
        
        Args:
            pin (int): GPIO pin number for buzzer (default: 5)
            beep_on_duration (float): Duration of beep ON phase in seconds
            beep_off_duration (float): Duration of beep OFF phase in seconds
        """
        self.pin = pin
        self.beep_on_duration = beep_on_duration
        self.beep_off_duration = beep_off_duration
        
        # Control flags
        self.is_active = False
        self.is_running = False
        self.thread = None
        self.buzzer = None
        
        # Safety and debounce
        self.last_activation_time = 0
        self.debounce_interval = 1.0  # Minimum 1 second between activations
        self.max_continuous_duration = 300  # Maximum 5 minutes continuous operation
        self.activation_start_time = None
        
        # Performance stats
        self.stats = {
            'activations': 0,
            'total_beep_time': 0,
            'errors': 0,
            'last_error': None
        }
        
        # Initialize GPIO
        self._initialize_gpio()
        
        logger.info(f"BuzzerController initialized on PIN {self.pin}")
    
    def _initialize_gpio(self):
        """Initialize GPIO buzzer with error handling and multiple fallback methods."""
        if not GPIO_AVAILABLE:
            logger.warning("GPIO not available - buzzer controller in simulation mode")
            return

        if not IS_RASPBERRY_PI:
            logger.warning("Not running on Raspberry Pi - buzzer controller in simulation mode")
            return

        logger.info("Initializing GPIO buzzer...")

        # Method 1: Try GPIO Zero with PiGPIO factory
        try:
            logger.info("Attempting GPIO Zero with PiGPIO factory...")
            Device.pin_factory = PiGPIOFactory()
            self.buzzer = Buzzer(self.pin)
            self.gpio_library = 'gpiozero_pigpio'
            logger.info(f"✅ GPIO Zero with PiGPIO buzzer initialized on PIN {self.pin}")
            return
        except Exception as e:
            logger.warning(f"GPIO Zero with PiGPIO failed: {e}")

        # Method 2: Try GPIO Zero with default factory
        try:
            logger.info("Attempting GPIO Zero with default factory...")
            # Reset pin factory to default
            Device.pin_factory.reset()
            self.buzzer = Buzzer(self.pin)
            self.gpio_library = 'gpiozero_default'
            logger.info(f"✅ GPIO Zero with default factory buzzer initialized on PIN {self.pin}")
            return
        except Exception as e:
            logger.warning(f"GPIO Zero with default factory failed: {e}")

        # Method 3: Try RPi.GPIO
        try:
            logger.info("Attempting RPi.GPIO...")
            GPIO.setmode(GPIO.BCM)
            GPIO.setup(self.pin, GPIO.OUT)
            GPIO.output(self.pin, GPIO.LOW)
            self.gpio_library = 'rpi_gpio'
            logger.info(f"✅ RPi.GPIO buzzer initialized on PIN {self.pin}")
            return
        except Exception as e:
            logger.warning(f"RPi.GPIO failed: {e}")

        # Method 4: Try direct sysfs GPIO (last resort)
        try:
            logger.info("Attempting direct sysfs GPIO...")
            # Export GPIO pin
            with open('/sys/class/gpio/export', 'w') as f:
                f.write(str(self.pin))

            # Set direction to output
            with open(f'/sys/class/gpio/gpio{self.pin}/direction', 'w') as f:
                f.write('out')

            # Set initial value to 0
            with open(f'/sys/class/gpio/gpio{self.pin}/value', 'w') as f:
                f.write('0')

            self.gpio_library = 'sysfs'
            logger.info(f"✅ Direct sysfs GPIO buzzer initialized on PIN {self.pin}")
            return
        except Exception as e:
            logger.warning(f"Direct sysfs GPIO failed: {e}")

        # All methods failed
        logger.error("All GPIO initialization methods failed - buzzer will run in simulation mode")
        self.stats['errors'] += 1
        self.stats['last_error'] = "All GPIO initialization methods failed"
        self.buzzer = None
        self.gpio_library = 'simulation'
    
    def activate(self, alarm_type="critical", reason="Critical alarm condition"):
        """
        Activate the buzzer for critical alarms.
        
        Args:
            alarm_type (str): Type of alarm triggering the buzzer
            reason (str): Reason for activation
            
        Returns:
            bool: True if activation successful, False otherwise
        """
        current_time = time.time()
        
        # Debounce protection
        if current_time - self.last_activation_time < self.debounce_interval:
            logger.debug(f"Buzzer activation debounced (last activation {current_time - self.last_activation_time:.1f}s ago)")
            return False
            
        # Check if already active
        if self.is_active:
            logger.debug("Buzzer already active")
            return True
            
        logger.info(f"Activating buzzer for {alarm_type} alarm: {reason}")
        
        self.is_active = True
        self.last_activation_time = current_time
        self.activation_start_time = current_time
        self.stats['activations'] += 1
        
        # Start buzzer thread
        if not self.is_running:
            self.is_running = True
            self.thread = threading.Thread(target=self._buzzer_loop, daemon=True)
            self.thread.start()
            logger.info("Buzzer thread started")
            
        return True
    
    def deactivate(self, reason="Alarm acknowledged"):
        """
        Deactivate the buzzer.
        
        Args:
            reason (str): Reason for deactivation
            
        Returns:
            bool: True if deactivation successful, False otherwise
        """
        if not self.is_active:
            logger.debug("Buzzer already inactive")
            return True
            
        logger.info(f"Deactivating buzzer: {reason}")
        
        self.is_active = False
        
        # Calculate total beep time for this activation
        if self.activation_start_time:
            beep_duration = time.time() - self.activation_start_time
            self.stats['total_beep_time'] += beep_duration
            logger.info(f"Buzzer was active for {beep_duration:.1f} seconds")
            
        # Turn off buzzer immediately
        self._turn_off_buzzer()
        
        return True
    
    def _buzzer_loop(self):
        """Main buzzer loop running in background thread."""
        logger.info("Buzzer loop started")
        
        while self.is_running:
            try:
                if self.is_active:
                    # Safety check: maximum continuous duration
                    if (self.activation_start_time and 
                        time.time() - self.activation_start_time > self.max_continuous_duration):
                        logger.warning(f"Buzzer auto-deactivated after {self.max_continuous_duration} seconds")
                        self.deactivate("Maximum duration exceeded")
                        break
                    
                    # Beep ON phase
                    self._turn_on_buzzer()
                    time.sleep(self.beep_on_duration)
                    
                    # Check if still active after ON phase
                    if not self.is_active:
                        break
                        
                    # Beep OFF phase
                    self._turn_off_buzzer()
                    time.sleep(self.beep_off_duration)
                else:
                    # Not active, sleep longer to save CPU
                    time.sleep(0.1)
                    
            except Exception as e:
                logger.error(f"Error in buzzer loop: {e}")
                self.stats['errors'] += 1
                self.stats['last_error'] = str(e)
                time.sleep(1)  # Prevent rapid error loops
                
        # Ensure buzzer is off when loop exits
        self._turn_off_buzzer()
        self.is_running = False
        logger.info("Buzzer loop stopped")

    def _turn_on_buzzer(self):
        """Turn on the buzzer using the available GPIO method."""
        if not GPIO_AVAILABLE or not IS_RASPBERRY_PI:
            logger.debug("Buzzer ON (simulation mode)")
            return

        try:
            if self.gpio_library in ['gpiozero_pigpio', 'gpiozero_default'] and self.buzzer:
                self.buzzer.on()
                logger.debug("Buzzer ON (GPIO Zero)")
            elif self.gpio_library == 'rpi_gpio':
                GPIO.output(self.pin, GPIO.HIGH)
                logger.debug("Buzzer ON (RPi.GPIO)")
            elif self.gpio_library == 'sysfs':
                with open(f'/sys/class/gpio/gpio{self.pin}/value', 'w') as f:
                    f.write('1')
                logger.debug("Buzzer ON (sysfs)")
            elif self.gpio_library == 'simulation':
                logger.debug("Buzzer ON (simulation mode)")
        except Exception as e:
            logger.error(f"Error turning on buzzer: {e}")
            self.stats['errors'] += 1
            self.stats['last_error'] = str(e)

    def _turn_off_buzzer(self):
        """Turn off the buzzer using the available GPIO method."""
        if not GPIO_AVAILABLE or not IS_RASPBERRY_PI:
            logger.debug("Buzzer OFF (simulation mode)")
            return

        try:
            if self.gpio_library in ['gpiozero_pigpio', 'gpiozero_default'] and self.buzzer:
                self.buzzer.off()
                logger.debug("Buzzer OFF (GPIO Zero)")
            elif self.gpio_library == 'rpi_gpio':
                GPIO.output(self.pin, GPIO.LOW)
                logger.debug("Buzzer OFF (RPi.GPIO)")
            elif self.gpio_library == 'sysfs':
                with open(f'/sys/class/gpio/gpio{self.pin}/value', 'w') as f:
                    f.write('0')
                logger.debug("Buzzer OFF (sysfs)")
            elif self.gpio_library == 'simulation':
                logger.debug("Buzzer OFF (simulation mode)")
        except Exception as e:
            logger.error(f"Error turning off buzzer: {e}")
            self.stats['errors'] += 1
            self.stats['last_error'] = str(e)

    def is_buzzer_active(self):
        """Check if buzzer is currently active."""
        return self.is_active

    def get_stats(self):
        """Get buzzer performance statistics."""
        return self.stats.copy()

    def cleanup(self):
        """Clean up GPIO resources."""
        logger.info("Cleaning up buzzer controller")

        # Deactivate buzzer
        self.deactivate("System shutdown")

        # Stop thread
        self.is_running = False
        if self.thread and self.thread.is_alive():
            self.thread.join(timeout=2)

        # Clean up GPIO based on the method used
        try:
            if self.gpio_library in ['gpiozero_pigpio', 'gpiozero_default'] and self.buzzer:
                self.buzzer.close()
                logger.info("GPIO Zero buzzer closed")
            elif self.gpio_library == 'rpi_gpio':
                GPIO.cleanup(self.pin)
                logger.info("RPi.GPIO cleaned up")
            elif self.gpio_library == 'sysfs':
                # Unexport the GPIO pin
                try:
                    with open('/sys/class/gpio/unexport', 'w') as f:
                        f.write(str(self.pin))
                    logger.info("sysfs GPIO unexported")
                except:
                    pass  # Pin might already be unexported
            elif self.gpio_library == 'simulation':
                logger.info("Simulation mode - no GPIO cleanup needed")
        except Exception as e:
            logger.error(f"Error during GPIO cleanup: {e}")

        logger.info("Buzzer controller cleanup completed")

# Singleton instance for global access
_buzzer_instance = None

def get_buzzer_controller():
    """Get the singleton buzzer controller instance."""
    global _buzzer_instance
    if _buzzer_instance is None:
        _buzzer_instance = BuzzerController()
    return _buzzer_instance

def cleanup_buzzer():
    """Clean up the buzzer controller singleton."""
    global _buzzer_instance
    if _buzzer_instance:
        _buzzer_instance.cleanup()
        _buzzer_instance = None
