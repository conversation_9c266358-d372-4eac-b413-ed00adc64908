#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Raspberry Pi 5 Setup and Optimization Script for TridentOS
Configures system for optimal performance with enhanced settings
"""

import os
import sys
import subprocess
import json
import platform

def is_raspberry_pi():
    """Check if running on Raspberry Pi"""
    try:
        with open('/proc/cpuinfo', 'r') as f:
            cpuinfo = f.read()
        return 'BCM' in cpuinfo and 'ARM' in cpuinfo
    except:
        return False

def check_system_requirements():
    """Check system requirements for TridentOS"""
    print("=== Checking System Requirements ===")
    
    # Check Python version
    python_version = sys.version_info
    if python_version.major >= 3 and python_version.minor >= 8:
        print(f"✓ Python {python_version.major}.{python_version.minor}.{python_version.micro}")
    else:
        print(f"✗ Python version too old: {python_version}")
        return False
    
    # Check if on Raspberry Pi
    if is_raspberry_pi():
        print("✓ Running on Raspberry Pi")
        
        # Check for required system files
        required_files = [
            '/sys/class/backlight/rpi_backlight/brightness',
            '/sys/class/backlight/rpi_backlight/max_brightness'
        ]
        
        for file_path in required_files:
            if os.path.exists(file_path):
                print(f"✓ Found: {file_path}")
            else:
                print(f"⚠ Missing: {file_path} (brightness control may not work)")
    else:
        print("⚠ Not running on Raspberry Pi (simulation mode)")
    
    return True

def setup_brightness_control():
    """Setup brightness control for Raspberry Pi"""
    print("\n=== Setting up Brightness Control ===")
    
    if not is_raspberry_pi():
        print("⚠ Skipping brightness setup (not on Raspberry Pi)")
        return True
    
    try:
        # Check if backlight control exists
        backlight_path = '/sys/class/backlight/rpi_backlight'
        if not os.path.exists(backlight_path):
            print("✗ Raspberry Pi backlight not found")
            return False
        
        # Get max brightness
        with open(f'{backlight_path}/max_brightness', 'r') as f:
            max_brightness = int(f.read().strip())
        print(f"✓ Max brightness: {max_brightness}")
        
        # Test brightness control
        test_brightness = max_brightness // 2
        try:
            subprocess.run(['sudo', 'sh', '-c', f'echo {test_brightness} > {backlight_path}/brightness'], 
                         check=True, capture_output=True)
            print(f"✓ Brightness control test successful")
        except subprocess.CalledProcessError as e:
            print(f"✗ Brightness control test failed: {e}")
            print("  You may need to add user to appropriate groups or configure sudo")
            return False
        
        return True
    except Exception as e:
        print(f"✗ Brightness setup error: {e}")
        return False

def setup_wifi_control():
    """Setup WiFi control for Raspberry Pi"""
    print("\n=== Setting up WiFi Control ===")
    
    if not is_raspberry_pi():
        print("⚠ Skipping WiFi setup (not on Raspberry Pi)")
        return True
    
    try:
        # Check for wireless interface
        result = subprocess.run(['iwconfig'], capture_output=True, text=True)
        if 'wlan0' in result.stdout:
            print("✓ WiFi interface wlan0 found")
        else:
            print("⚠ WiFi interface wlan0 not found")
        
        # Check for required tools
        tools = ['iwlist', 'wpa_supplicant', 'dhclient']
        for tool in tools:
            try:
                subprocess.run(['which', tool], check=True, capture_output=True)
                print(f"✓ Found tool: {tool}")
            except subprocess.CalledProcessError:
                print(f"✗ Missing tool: {tool}")
                return False
        
        # Test WiFi scanning
        try:
            result = subprocess.run(['sudo', 'iwlist', 'wlan0', 'scan'], 
                                  capture_output=True, text=True, timeout=10)
            if result.returncode == 0:
                print("✓ WiFi scanning test successful")
            else:
                print(f"⚠ WiFi scanning test failed: {result.stderr}")
        except subprocess.TimeoutExpired:
            print("⚠ WiFi scanning test timed out")
        except Exception as e:
            print(f"⚠ WiFi scanning test error: {e}")
        
        return True
    except Exception as e:
        print(f"✗ WiFi setup error: {e}")
        return False

def setup_gpio_permissions():
    """Setup GPIO permissions for buzzer and other hardware"""
    print("\n=== Setting up GPIO Permissions ===")
    
    if not is_raspberry_pi():
        print("⚠ Skipping GPIO setup (not on Raspberry Pi)")
        return True
    
    try:
        # Check if user is in gpio group
        import grp
        try:
            gpio_group = grp.getgrnam('gpio')
            current_user = os.getenv('USER')
            if current_user in gpio_group.gr_mem:
                print(f"✓ User {current_user} is in gpio group")
            else:
                print(f"⚠ User {current_user} is not in gpio group")
                print("  Run: sudo usermod -a -G gpio $USER")
        except KeyError:
            print("⚠ GPIO group not found")
        
        # Check GPIO access
        gpio_path = '/sys/class/gpio'
        if os.path.exists(gpio_path):
            print(f"✓ GPIO interface available: {gpio_path}")
        else:
            print(f"✗ GPIO interface not found: {gpio_path}")
            return False
        
        return True
    except Exception as e:
        print(f"✗ GPIO setup error: {e}")
        return False

def optimize_for_touchscreen():
    """Optimize system for touchscreen use"""
    print("\n=== Optimizing for Touchscreen ===")
    
    optimizations = {
        'cursor_hide': 'Hide mouse cursor for touchscreen',
        'touch_calibration': 'Touch calibration settings',
        'screen_rotation': 'Screen rotation settings'
    }
    
    for opt, desc in optimizations.items():
        print(f"⚠ Manual setup required: {desc}")
    
    # Create touchscreen optimization script
    touchscreen_script = """#!/bin/bash
# TridentOS Touchscreen Optimization Script

# Hide mouse cursor
export DISPLAY=:0
unclutter -idle 1 -root &

# Set screen rotation if needed
# xrandr --output HDMI-1 --rotate left

# Disable screen blanking
xset s off
xset -dpms
xset s noblank

echo "Touchscreen optimizations applied"
"""
    
    script_path = 'touchscreen_setup.sh'
    with open(script_path, 'w') as f:
        f.write(touchscreen_script)
    
    os.chmod(script_path, 0o755)
    print(f"✓ Created touchscreen setup script: {script_path}")
    
    return True

def create_systemd_service():
    """Create systemd service for TridentOS autostart"""
    print("\n=== Creating Systemd Service ===")
    
    if not is_raspberry_pi():
        print("⚠ Skipping systemd service (not on Raspberry Pi)")
        return True
    
    service_content = f"""[Unit]
Description=TridentOS Marine Control System
After=graphical-session.target
Wants=graphical-session.target

[Service]
Type=simple
User=pi
Environment=DISPLAY=:0
WorkingDirectory={os.getcwd()}
ExecStartPre=/bin/bash touchscreen_setup.sh
ExecStart=/usr/bin/python3 main.py
Restart=always
RestartSec=5

[Install]
WantedBy=graphical-session.target
"""
    
    service_path = 'tridentos.service'
    with open(service_path, 'w') as f:
        f.write(service_content)
    
    print(f"✓ Created systemd service file: {service_path}")
    print("  To install: sudo cp tridentos.service /etc/systemd/system/")
    print("  To enable: sudo systemctl enable tridentos.service")
    print("  To start: sudo systemctl start tridentos.service")
    
    return True

def install_dependencies():
    """Install required Python dependencies"""
    print("\n=== Installing Dependencies ===")
    
    dependencies = [
        'kivy[base]>=2.3.0',
        'kivymd>=1.1.0',
        'RPi.GPIO;platform_machine=="armv7l"',
        'gpiozero;platform_machine=="armv7l"'
    ]
    
    for dep in dependencies:
        try:
            print(f"Installing {dep}...")
            subprocess.run([sys.executable, '-m', 'pip', 'install', dep], 
                         check=True, capture_output=True)
            print(f"✓ Installed: {dep}")
        except subprocess.CalledProcessError as e:
            print(f"⚠ Failed to install {dep}: {e}")
    
    return True

def create_config_files():
    """Create optimized configuration files"""
    print("\n=== Creating Configuration Files ===")
    
    # Kivy config for Raspberry Pi
    kivy_config = """[graphics]
fullscreen = 1
borderless = 1
window_state = maximized

[input]
mouse = mouse
%(name)s = probesysfs,provider=hidinput

[postproc]
double_tap_time = 250
double_tap_distance = 20
"""
    
    config_dir = os.path.expanduser('~/.kivy')
    os.makedirs(config_dir, exist_ok=True)
    
    config_path = os.path.join(config_dir, 'config.ini')
    with open(config_path, 'w') as f:
        f.write(kivy_config)
    
    print(f"✓ Created Kivy config: {config_path}")
    
    # TridentOS specific config
    trident_config = {
        'raspberry_pi_optimizations': {
            'fullscreen': True,
            'hide_cursor': True,
            'disable_screensaver': True,
            'touch_optimized': True
        },
        'hardware': {
            'buzzer_pin': 5,
            'brightness_control': True,
            'wifi_interface': 'wlan0'
        },
        'performance': {
            'cache_size': 50,
            'gc_threshold': 100,
            'memory_limit_mb': 512
        }
    }
    
    with open('trident_config.json', 'w') as f:
        json.dump(trident_config, f, indent=2)
    
    print("✓ Created TridentOS config: trident_config.json")
    
    return True

def main():
    """Main setup function"""
    print("TridentOS Raspberry Pi 5 Setup Script")
    print("=" * 50)
    
    setup_functions = [
        check_system_requirements,
        install_dependencies,
        setup_brightness_control,
        setup_wifi_control,
        setup_gpio_permissions,
        optimize_for_touchscreen,
        create_config_files,
        create_systemd_service
    ]
    
    success_count = 0
    total_count = len(setup_functions)
    
    for setup_func in setup_functions:
        try:
            if setup_func():
                success_count += 1
        except Exception as e:
            print(f"✗ Setup function {setup_func.__name__} failed: {e}")
    
    print("\n" + "=" * 50)
    print(f"Setup Results: {success_count}/{total_count} steps completed")
    
    if success_count == total_count:
        print("🎉 TridentOS setup completed successfully!")
        print("\nNext steps:")
        print("1. Reboot the Raspberry Pi")
        print("2. Test the application: python3 main.py")
        print("3. Install systemd service if desired")
    else:
        print("⚠️  Some setup steps failed. Check the output above.")
        print("The application may still work with reduced functionality.")
    
    return 0 if success_count == total_count else 1

if __name__ == "__main__":
    exit(main())
