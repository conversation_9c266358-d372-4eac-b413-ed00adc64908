# TridentOS Enhanced Settings Documentation

## Przegląd nowych funkcjonalności

TridentOS został rozszerzony o zaawansowane funkcjonalności menu ustawień, które obejmują:

### 🌍 **Rozszerzone pakiety językowe**
- **<PERSON><PERSON><PERSON>** (English) - j<PERSON><PERSON><PERSON> do<PERSON>y
- **Polski** (Polski) - pełne tłumaczenie interfejsu
- **<PERSON><PERSON><PERSON><PERSON>** (Deutsch) - dla użytkowników niemieckojęzycznych
- **R<PERSON><PERSON>ski** (Русский) - dla użytkowników rosyjskojęzycznych

### 🔆 **Kontrola jasności ekranu**
- Suwak jasności od 10% do 100%
- Rzeczywista kontrola jasności na Raspberry Pi
- Symulacja na PC dla celów testowych
- Automatyczne zapisywanie ustawień

### 🎨 **System motywów**
- **Tryb dzienny** - jasny motyw dla lepszej widoczności w dzień
- **Tryb nocny** - ciemny motyw dla komfortu w nocy
- Automatyczne przełączanie kolorów interfejsu
- Optymalizacja dla środowiska morskiego

### 🔐 **System kodu PIN**
- Ustawianie 4-cyfrowego kodu PIN
- Ochrona dostępu do ustawień
- Weryfikacja PIN przy każdym dostępie
- Bezpieczne przechowywanie w ustawieniach

### 📶 **Zarządzanie WiFi**
- Skanowanie dostępnych sieci WiFi
- Lista dostępnych sieci w czasie rzeczywistym
- Łączenie z sieciami z hasłem
- Status połączenia
- Obsługa sieci otwartych i zabezpieczonych

### ⌨️ **Klawiatura ekranowa**
- Automatyczne pojawianie się przy wprowadzaniu tekstu
- Obsługa tekstu i haseł (maskowanie)
- Klawiatura numeryczna dla liczb
- Klawiatura QWERTY dla tekstu
- Optymalizacja dla ekranów dotykowych

## Struktura plików

```
venv/TridentUI2/
├── main.py                          # Główna aplikacja z rozszerzoną klasą SettingsScreen
├── translations.py                  # System tłumaczeń dla 4 języków
├── widgets/
│   └── virtual_keyboard.py         # Widget klawiatury ekranowej
├── ui/
│   └── settings.kv                 # Rozszerzony interfejs ustawień
├── settings.json                   # Plik konfiguracyjny z nowymi ustawieniami
└── test_enhanced_settings.py       # Testy wszystkich nowych funkcjonalności
```

## Konfiguracja ustawień

### Struktura pliku settings.json

```json
{
  "display": {
    "brightness": 80,
    "theme_mode": "Night",
    "orientation": "Landscape"
  },
  "language": {
    "language": "English",
    "time_format": "24h",
    "units": "Metric"
  },
  "safety": {
    "lock_enabled": false,
    "emergency_contact": "VHF Channel 16",
    "secure_mode": false,
    "pin_code": "",
    "pin_enabled": false
  },
  "connection": {
    "wifi_enabled": true,
    "bluetooth_enabled": true,
    "nmea_id": "001"
  },
  "calibration": {
    "water_zero": 0.0,
    "fuel_zero": 0.0
  }
}
```

## Użycie funkcjonalności

### 1. Zmiana języka
1. Przejdź do **SETTINGS** → **LANGUAGE**
2. Wybierz jeden z 4 dostępnych języków
3. Interfejs automatycznie się przetłumaczy

### 2. Kontrola jasności
1. Przejdź do **SETTINGS** → **DISPLAY**
2. Użyj suwaka **Brightness** (10-100%)
3. Na Raspberry Pi jasność ekranu zmieni się natychmiast

### 3. Zmiana motywu
1. Przejdź do **SETTINGS** → **DISPLAY**
2. Naciśnij przycisk **Theme Mode**
3. Przełącz między trybem **Day** i **Night**

### 4. Ustawienie kodu PIN
1. Przejdź do **SETTINGS** → **SAFETY**
2. Wprowadź 4-cyfrowy PIN w polu tekstowym
3. Naciśnij **Set PIN**
4. Włącz **PIN Protection**

### 5. Zarządzanie WiFi
1. Przejdź do **SETTINGS** → **CONNECTION**
2. Włącz **WiFi** jeśli wyłączony
3. Naciśnij **Scan Networks**
4. Wybierz sieć z listy
5. Wprowadź hasło i naciśnij **Connect**

### 6. Klawiatura ekranowa
- Pojawia się automatycznie przy kliknięciu w pole tekstowe
- Obsługuje tekst, hasła i liczby
- Znika automatycznie po zakończeniu wprowadzania

## Optymalizacje dla Raspberry Pi 5

### Kontrola jasności
```bash
# Rzeczywista kontrola jasności na RPi5
echo 191 > /sys/class/backlight/rpi_backlight/brightness
```

### Skanowanie WiFi
```bash
# Skanowanie sieci WiFi na RPi5
sudo iwlist wlan0 scan
```

### Łączenie z WiFi
```bash
# Konfiguracja wpa_supplicant
sudo wpa_supplicant -B -i wlan0 -c /tmp/wifi_config.conf
sudo dhclient wlan0
```

## Testowanie

Uruchom testy wszystkich funkcjonalności:

```bash
cd venv/TridentUI2
python test_enhanced_settings.py
```

Testy sprawdzają:
- ✅ System tłumaczeń (4 języki)
- ✅ Klawiaturę ekranową
- ✅ Zapisywanie/wczytywanie ustawień
- ✅ Symulację WiFi
- ✅ Funkcjonalność PIN
- ✅ Kontrolę jasności
- ✅ System motywów

## Bezpieczeństwo

### Ochrona PIN
- PIN jest przechowywany w pliku settings.json
- Weryfikacja przy każdym dostępie do ustawień
- Minimum 4 cyfry dla bezpieczeństwa

### WiFi
- Hasła WiFi nie są przechowywane permanentnie
- Bezpieczne łączenie przez wpa_supplicant
- Skanowanie tylko dostępnych sieci

## Kompatybilność

### Raspberry Pi 5
- Pełna funkcjonalność
- Rzeczywista kontrola jasności
- Skanowanie i łączenie WiFi
- Optymalizacje wydajności

### PC/Windows
- Symulacja funkcjonalności
- Testowanie interfejsu
- Pełna funkcjonalność UI

## Rozwiązywanie problemów

### Klawiatura nie pojawia się
- Sprawdź import `widgets.virtual_keyboard`
- Upewnij się, że Factory.register został wywołany

### Tłumaczenia nie działają
- Sprawdź plik `translations.py`
- Upewnij się, że język jest w dostępnej liście

### WiFi nie skanuje
- Na RPi5: sprawdź uprawnienia sudo
- Na PC: używana jest symulacja

### Jasność nie zmienia się
- Na RPi5: sprawdź ścieżkę `/sys/class/backlight/rpi_backlight/`
- Na PC: tylko symulacja

## Przyszłe rozszerzenia

- 🔄 Automatyczne przełączanie motywów według czasu
- 🌐 Więcej języków (hiszpański, francuski, włoski)
- 🔊 Kontrola głośności systemu
- 📱 Obsługa gestów dotykowych
- 🛰️ Integracja z GPS dla automatycznych ustawień
