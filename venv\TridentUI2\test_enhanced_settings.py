#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Test script for enhanced TridentOS settings functionality
Tests: Language packages, brightness control, themes, PIN code, WiFi, virtual keyboard
"""

import os
import sys
import json

# Add current directory to path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_translations():
    """Test translation system"""
    print("=== Testing Translation System ===")
    try:
        from translations import get_translation, get_available_languages
        
        # Test available languages
        languages = get_available_languages()
        print(f"Available languages: {languages}")
        
        # Test translations for each language
        test_key = "SETTINGS"
        for lang in languages:
            translation = get_translation(test_key, lang)
            print(f"{lang}: {test_key} -> {translation}")
        
        print("✓ Translation system working correctly")
        return True
    except Exception as e:
        print(f"✗ Translation system error: {e}")
        return False

def test_virtual_keyboard():
    """Test virtual keyboard widget"""
    print("\n=== Testing Virtual Keyboard ===")
    try:
        from widgets.virtual_keyboard import <PERSON><PERSON>eyboard, VirtualKeyboardPopup
        print("✓ Virtual keyboard imports successful")

        # Test keyboard creation with parameters
        keyboard = VirtualKeyboard(is_password=True, is_numeric=True)
        print(f"✓ Virtual keyboard created: {type(keyboard)}")

        # Test text input functionality
        keyboard.add_character("1")
        keyboard.add_character("2")
        keyboard.add_character("3")
        keyboard.add_character("4")

        assert keyboard.text_input == "1234"
        print("✓ Text input functionality working")

        # Test backspace
        keyboard.backspace()
        assert keyboard.text_input == "123"
        print("✓ Backspace functionality working")

        # Test clear
        keyboard.clear_text()
        assert keyboard.text_input == ""
        print("✓ Clear functionality working")

        # Test popup creation
        popup = VirtualKeyboardPopup(is_password=True)
        print(f"✓ Virtual keyboard popup created: {type(popup)}")

        return True
    except Exception as e:
        print(f"✗ Virtual keyboard error: {e}")
        return False

def test_settings_persistence():
    """Test settings save/load functionality"""
    print("\n=== Testing Settings Persistence ===")
    try:
        # Test settings file structure
        test_settings = {
            'display': {
                'brightness': 75,
                'theme_mode': 'Night',
                'orientation': 'Landscape'
            },
            'language': {
                'language': 'Polski',
                'time_format': '24h',
                'units': 'Metric'
            },
            'safety': {
                'lock_enabled': True,
                'emergency_contact': 'VHF Channel 16',
                'secure_mode': False,
                'pin_code': '1234',
                'pin_enabled': True
            },
            'connection': {
                'wifi_enabled': True,
                'bluetooth_enabled': True,
                'nmea_id': '001'
            },
            'calibration': {
                'water_zero': 0.0,
                'fuel_zero': 0.0
            }
        }
        
        # Test JSON serialization
        test_file = 'test_settings.json'
        with open(test_file, 'w') as f:
            json.dump(test_settings, f, indent=2)
        
        # Test JSON loading
        with open(test_file, 'r') as f:
            loaded_settings = json.load(f)
        
        # Verify data integrity
        assert loaded_settings == test_settings
        
        # Cleanup
        os.remove(test_file)
        
        print("✓ Settings persistence working correctly")
        return True
    except Exception as e:
        print(f"✗ Settings persistence error: {e}")
        return False

def test_wifi_simulation():
    """Test WiFi functionality simulation"""
    print("\n=== Testing WiFi Simulation ===")
    try:
        # Simulate network scanning
        simulated_networks = ["TridentOS_Network", "Marina_WiFi", "Yacht_Club", "Harbor_Guest"]
        print(f"Simulated networks: {simulated_networks}")
        
        # Simulate connection
        selected_network = simulated_networks[0]
        password = "test_password"
        print(f"Simulating connection to: {selected_network} with password: {'*' * len(password)}")
        
        # Test network validation
        assert len(selected_network) > 0
        assert len(password) >= 8  # Minimum password length
        
        print("✓ WiFi simulation working correctly")
        return True
    except Exception as e:
        print(f"✗ WiFi simulation error: {e}")
        return False

def test_pin_functionality():
    """Test PIN code functionality"""
    print("\n=== Testing PIN Functionality ===")
    try:
        # Test PIN validation
        valid_pins = ["1234", "0000", "9999", "5678"]
        invalid_pins = ["123", "12345", "abcd", ""]
        
        for pin in valid_pins:
            assert len(pin) == 4 and pin.isdigit()
            print(f"✓ Valid PIN: {pin}")
        
        for pin in invalid_pins:
            assert not (len(pin) == 4 and pin.isdigit())
            print(f"✓ Invalid PIN rejected: {pin}")
        
        print("✓ PIN functionality working correctly")
        return True
    except Exception as e:
        print(f"✗ PIN functionality error: {e}")
        return False

def test_brightness_control():
    """Test brightness control functionality"""
    print("\n=== Testing Brightness Control ===")
    try:
        # Test brightness range validation
        valid_brightness = [0, 25, 50, 75, 100]
        invalid_brightness = [-10, 150, 200]
        
        for brightness in valid_brightness:
            assert 0 <= brightness <= 100
            print(f"✓ Valid brightness: {brightness}%")
        
        for brightness in invalid_brightness:
            assert not (0 <= brightness <= 100)
            print(f"✓ Invalid brightness rejected: {brightness}%")
        
        # Test brightness calculation for RPi
        for brightness in valid_brightness:
            rpi_value = int((brightness / 100) * 255)
            assert 0 <= rpi_value <= 255
            print(f"✓ Brightness {brightness}% -> RPi value: {rpi_value}/255")
        
        print("✓ Brightness control working correctly")
        return True
    except Exception as e:
        print(f"✗ Brightness control error: {e}")
        return False

def test_theme_system():
    """Test theme system functionality"""
    print("\n=== Testing Theme System ===")
    try:
        # Test theme modes
        themes = ["Day", "Night"]
        
        for theme in themes:
            print(f"✓ Theme mode: {theme}")
            
            # Test theme colors (example)
            if theme == "Day":
                bg_color = (0.9, 0.9, 0.9, 1)  # Light background
                text_color = (0.1, 0.1, 0.1, 1)  # Dark text
            else:  # Night
                bg_color = (0.05, 0.1, 0.15, 1)  # Dark background
                text_color = (1, 1, 1, 1)  # Light text
            
            print(f"  Background: {bg_color}")
            print(f"  Text: {text_color}")
        
        print("✓ Theme system working correctly")
        return True
    except Exception as e:
        print(f"✗ Theme system error: {e}")
        return False

def main():
    """Run all tests"""
    print("TridentOS Enhanced Settings Test Suite")
    print("=" * 50)
    
    tests = [
        test_translations,
        test_virtual_keyboard,
        test_settings_persistence,
        test_wifi_simulation,
        test_pin_functionality,
        test_brightness_control,
        test_theme_system
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        try:
            if test():
                passed += 1
        except Exception as e:
            print(f"✗ Test failed with exception: {e}")
    
    print("\n" + "=" * 50)
    print(f"Test Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All tests passed! Enhanced settings are ready.")
        return 0
    else:
        print("⚠️  Some tests failed. Check the implementation.")
        return 1

if __name__ == "__main__":
    exit(main())
