import json
import time
import uuid
import state_manager
from datetime import datetime
import logging
from functools import lru_cache

# Import buzzer controller
try:
    from buzzer_controller import get_buzzer_controller
    BUZZER_AVAILABLE = True
except ImportError:
    BUZZER_AVAILABLE = False
    def get_buzzer_controller():
        return None

# Wykrywanie platformy dla optymalizacji
import platform
IS_RASPBERRY_PI = platform.machine().startswith('arm') or 'raspberry' in platform.node().lower()

# Configure logging - zoptymalizowane dla RPi5
if IS_RASPBERRY_PI:
    # Minimalne logowanie na RPi5 dla oszczędności CPU
    logging.basicConfig(
        level=logging.ERROR,  # Tylko błędy na RPi5
        format='%(levelname)s: %(message)s'  # Uproszczony format
    )
else:
    # Standardowe logowanie na PC
    logging.basicConfig(
        level=logging.WARNING,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )
logger = logging.getLogger("AlarmManager")

# Alarm priority levels according to IMO MSC.302(87) Bridge Alert Management
ALARM_EMERGENCY = "emergency"  # Alarms requiring immediate action (highest priority)
ALARM_CRITICAL = "critical"    # Critical alarms requiring immediate attention
ALARM_WARNING = "warning"      # Warning conditions requiring attention but not immediate action
ALARM_CAUTION = "caution"      # Caution conditions requiring awareness
ALARM_INFO = "info"            # Informational messages (lowest priority)

# Alarm categories according to maritime standards
CATEGORY_A = "A"  # Category A: Alarms requiring immediate action
CATEGORY_B = "B"  # Category B: Alarms requiring timely action/attention
CATEGORY_C = "C"  # Category C: Warnings and cautions requiring awareness

# Stałe definiujące źródła alarmów
SOURCE_BATTERY = "Battery"
SOURCE_CLIMATE = "Climate"
SOURCE_ENGINE = "Engine"
SOURCE_WATER = "Water"
SOURCE_FUEL = "Fuel"
SOURCE_SYSTEM = "System"
SOURCE_NAVIGATION = "Navigation"
SOURCE_COMMUNICATION = "Communication"
SOURCE_SAFETY = "Safety"

# Statystyki wydajności - zoptymalizowane dla RPi5
_performance_stats = {
    'load_count': 0,
    'save_count': 0,
    'alarm_checks': 0,
    'alarm_updates': 0,
    'last_cleanup_time': 0,
    'cache_hits': 0,
    'memory_usage': 0
}

# Parametry wydajności dostosowane do platformy
if IS_RASPBERRY_PI:
    _max_active_alarms = 50      # Limit aktywnych alarmów na RPi5
    _max_history_alarms = 100    # Limit historii alarmów na RPi5
    _cleanup_interval = 300      # Czyszczenie co 5 minut na RPi5
    _batch_size = 5              # Mniejsze partie operacji na RPi5
else:
    _max_active_alarms = 100     # Więcej alarmów na PC
    _max_history_alarms = 500    # Większa historia na PC
    _cleanup_interval = 60       # Częstsze czyszczenie na PC
    _batch_size = 10             # Większe partie na PC

class AlarmManager:
    def __init__(self):
        # Wczytanie alarmów z pliku stanu
        self.load_alarms()
        logger.info("AlarmManager initialized")

        # Initialize alarm escalation timers
        self.escalation_timers = {}

        # Initialize alarm groups for related alarms
        self.alarm_groups = {}

        # Initialize alarm suppression rules
        self.suppression_rules = {}

        # Initialize alarm shelving (temporary suppression)
        self.shelved_alarms = {}

        # Initialize buzzer controller
        self.buzzer_controller = get_buzzer_controller() if BUZZER_AVAILABLE else None
        if self.buzzer_controller:
            logger.info("Buzzer controller integrated with alarm manager")
        else:
            logger.warning("Buzzer controller not available")

    @lru_cache(maxsize=1)
    def _get_default_alarms(self):
        """
        Zwraca domyślną strukturę alarmów z pamięcią podręczną.
        """
        return {
            'active': [],
            'history': [],
            'shelved': [],
            'suppressed': [],
            'last_update': datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        }

    def load_alarms(self, force_refresh=False):
        """
        Loads alarms from system_state.json with error handling and data validation.
        Zoptymalizowana wersja z pamięcią podręczną i redukcją operacji I/O.

        Args:
            force_refresh (bool): Wymusza odświeżenie alarmów z pliku
        """
        # Aktualizacja statystyk
        _performance_stats['load_count'] += 1

        # Jeśli alarmy są już załadowane i nie wymuszono odświeżenia, zwróć je
        if hasattr(self, 'alarms') and self.alarms and not force_refresh:
            return

        try:
            # Load state with force_refresh only when explicitly requested
            state_data = state_manager.load_state(force_refresh=force_refresh)

            # Create default alarm structure
            default_alarms = self._get_default_alarms()

            # If no alarms section exists, create it
            if 'alarms' not in state_data:
                state_data['alarms'] = default_alarms
                state_manager.save_state(state_data)
                self.alarms = default_alarms
                return

            # Validate alarm structure
            alarms = state_data.get('alarms', default_alarms)

            # Ensure all required sections exist
            for section in ['active', 'history', 'shelved', 'suppressed']:
                if section not in alarms or not isinstance(alarms[section], list):
                    alarms[section] = []

            if 'last_update' not in alarms:
                alarms['last_update'] = datetime.now().strftime("%Y-%m-%d %H:%M:%S")

            # Optymalizacja: Sprawdzanie duplikatów i nieaktywnych alarmów tylko gdy wymuszono odświeżenie
            if force_refresh:
                # Check for duplicate alarms in active list
                active_alarm_ids = set()
                duplicate_ids = []

                for alarm in alarms['active']:
                    if 'id' not in alarm:
                        continue

                    alarm_id = alarm['id']
                    if alarm_id in active_alarm_ids:
                        duplicate_ids.append(alarm_id)
                    else:
                        active_alarm_ids.add(alarm_id)

                # Remove duplicate alarms (keep only the first occurrence)
                if duplicate_ids:
                    for alarm_id in duplicate_ids:
                        # Find all occurrences of this ID
                        occurrences = [i for i, alarm in enumerate(alarms['active']) if alarm.get('id') == alarm_id]
                        # Keep the first one, remove the rest
                        for index in sorted(occurrences[1:], reverse=True):
                            del alarms['active'][index]

                # Check for inactive alarms in active list
                inactive_alarms = [alarm for alarm in alarms['active'] if not alarm.get('active', True)]
                if inactive_alarms:
                    # Move inactive alarms to history
                    for alarm in inactive_alarms:
                        if 'deactivate_time' not in alarm:
                            alarm['deactivate_time'] = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
                        alarms['history'].append(alarm.copy())

                    # Remove inactive alarms from active list
                    alarms['active'] = [alarm for alarm in alarms['active'] if alarm.get('active', True)]

                # Save the cleaned up alarms
                if duplicate_ids or inactive_alarms:
                    state_data['alarms'] = alarms
                    state_manager.save_state(state_data)
                    _performance_stats['save_count'] += 1

            # Store the validated alarms
            self.alarms = alarms

            # Ensure all alarms have the required fields for maritime standards
            # Optymalizacja: Sprawdzanie pól tylko dla nowych alarmów
            for alarm in self.alarms['active']:
                if not alarm.get('_validated', False):
                    self._ensure_maritime_fields(alarm)
                    alarm['_validated'] = True

            # Initialize alarm groups
            self.alarm_groups = {}
            for alarm in self.alarms['active']:
                if 'alarm_group' in alarm and 'id' in alarm:
                    group = alarm['alarm_group']
                    if group not in self.alarm_groups:
                        self.alarm_groups[group] = []
                    self.alarm_groups[group].append(alarm['id'])

        except Exception as e:
            logger.error(f"Error loading alarms: {e}")

            # Use default structure if loading fails
            self.alarms = self._get_default_alarms()

    def _ensure_maritime_fields(self, alarm):
        """Ensures that an alarm has all the required fields for maritime standards"""
        # Add category if not present
        if 'category' not in alarm:
            if alarm['type'] == ALARM_EMERGENCY or alarm['type'] == ALARM_CRITICAL:
                alarm['category'] = CATEGORY_A
            elif alarm['type'] == ALARM_WARNING:
                alarm['category'] = CATEGORY_B
            else:
                alarm['category'] = CATEGORY_C

        # Add escalation level if not present
        if 'escalation_level' not in alarm:
            alarm['escalation_level'] = 0

        # Add responsibility if not present
        if 'responsibility' not in alarm:
            alarm['responsibility'] = 'Bridge'

        # Add alarm_group if not present
        if 'alarm_group' not in alarm:
            alarm['alarm_group'] = alarm['source']

        # Add suppression_allowed if not present
        if 'suppression_allowed' not in alarm:
            # Critical and emergency alarms cannot be suppressed
            alarm['suppression_allowed'] = not (alarm['type'] == ALARM_EMERGENCY or alarm['type'] == ALARM_CRITICAL)

        # Add shelving_allowed if not present
        if 'shelving_allowed' not in alarm:
            # Emergency alarms cannot be shelved
            alarm['shelving_allowed'] = alarm['type'] != ALARM_EMERGENCY

        # Add escalation_time if not present
        if 'escalation_time' not in alarm:
            # Default escalation time: 60 seconds for critical, 120 for warnings, 300 for others
            if alarm['type'] == ALARM_CRITICAL:
                alarm['escalation_time'] = 60
            elif alarm['type'] == ALARM_WARNING:
                alarm['escalation_time'] = 120
            else:
                alarm['escalation_time'] = 300

        # Add visual_indication if not present
        if 'visual_indication' not in alarm:
            if alarm['type'] == ALARM_EMERGENCY:
                alarm['visual_indication'] = 'flashing_red'
            elif alarm['type'] == ALARM_CRITICAL:
                alarm['visual_indication'] = 'red'
            elif alarm['type'] == ALARM_WARNING:
                alarm['visual_indication'] = 'yellow'
            elif alarm['type'] == ALARM_CAUTION:
                alarm['visual_indication'] = 'amber'
            else:
                alarm['visual_indication'] = 'blue'

        # Add audio_indication if not present
        if 'audio_indication' not in alarm:
            if alarm['type'] == ALARM_EMERGENCY:
                alarm['audio_indication'] = 'continuous'
            elif alarm['type'] == ALARM_CRITICAL:
                alarm['audio_indication'] = 'intermittent'
            elif alarm['type'] == ALARM_WARNING:
                alarm['audio_indication'] = 'single'
            else:
                alarm['audio_indication'] = 'none'

        return alarm

    def save_alarms(self, force_save=False):
        """
        Zapisuje alarmy do pliku system_state.json

        Args:
            force_save (bool): Wymusza zapis nawet jeśli nie ma zmian
        """
        # Aktualizacja statystyk
        _performance_stats['save_count'] += 1

        # Sprawdzenie, czy alarmy istnieją
        if not hasattr(self, 'alarms') or not self.alarms:
            return

        # Optymalizacja: Sprawdzenie, czy są aktywne alarmy przed wczytaniem stanu
        has_active_alarms = len(self.alarms['active']) > 0

        # Optymalizacja: Wczytanie stanu tylko jeśli są aktywne alarmy lub wymuszono zapis
        if has_active_alarms or force_save:
            state_data = state_manager.load_state()
            state_data['alarms'] = self.alarms

            # Optymalizacja: Obliczanie flag tylko jeśli są aktywne alarmy
            if has_active_alarms:
                # Przygotowanie list alarmów według typów - jednorazowe przejście przez listę
                emergency_alarms = []
                critical_alarms = []
                warning_alarms = []
                caution_alarms = []
                info_alarms = []
                category_a_alarms = []
                category_b_alarms = []
                category_c_alarms = []

                # Jednorazowe przejście przez listę alarmów
                for alarm in self.alarms['active']:
                    alarm_type = alarm['type']
                    if alarm_type == ALARM_EMERGENCY:
                        emergency_alarms.append(alarm)
                    elif alarm_type == ALARM_CRITICAL:
                        critical_alarms.append(alarm)
                    elif alarm_type == ALARM_WARNING:
                        warning_alarms.append(alarm)
                    elif alarm_type == ALARM_CAUTION:
                        caution_alarms.append(alarm)
                    elif alarm_type == ALARM_INFO:
                        info_alarms.append(alarm)

                    # Sprawdzenie kategorii
                    category = alarm.get('category')
                    if category == CATEGORY_A:
                        category_a_alarms.append(alarm)
                    elif category == CATEGORY_B:
                        category_b_alarms.append(alarm)
                    elif category == CATEGORY_C:
                        category_c_alarms.append(alarm)

                # Update alarm status flags
                state_data['has_active_alarms'] = has_active_alarms
                state_data['has_critical_alarms'] = len(critical_alarms) > 0
                state_data['has_emergency_alarms'] = len(emergency_alarms) > 0

                # Add maritime-specific status flags
                state_data['has_category_a_alarms'] = len(category_a_alarms) > 0
                state_data['has_category_b_alarms'] = len(category_b_alarms) > 0
                state_data['has_category_c_alarms'] = len(category_c_alarms) > 0

                # Add alarm counts by type
                state_data['alarm_counts'] = {
                    'emergency': len(emergency_alarms),
                    'critical': len(critical_alarms),
                    'warning': len(warning_alarms),
                    'caution': len(caution_alarms),
                    'info': len(info_alarms),
                    'total': len(self.alarms['active'])
                }
            else:
                # Jeśli nie ma aktywnych alarmów, ustaw wszystkie flagi na False
                state_data['has_active_alarms'] = False
                state_data['has_critical_alarms'] = False
                state_data['has_emergency_alarms'] = False
                state_data['has_category_a_alarms'] = False
                state_data['has_category_b_alarms'] = False
                state_data['has_category_c_alarms'] = False
                state_data['alarm_counts'] = {
                    'emergency': 0,
                    'critical': 0,
                    'warning': 0,
                    'caution': 0,
                    'info': 0,
                    'total': 0
                }

            # Zapisanie stanu
            state_manager.save_state(state_data)

    def add_alarm(self, alarm_type, message, source, category=None, responsibility=None,
                 alarm_group=None, additional_info=None):
        """
        Adds a new alarm to the system following maritime standards

        Args:
            alarm_type: Type/priority of the alarm (emergency, critical, warning, caution, info)
            message: The alarm message
            source: Source system of the alarm
            category: Alarm category (A, B, C) according to maritime standards
            responsibility: Who is responsible for handling this alarm
            alarm_group: Group for related alarms
            additional_info: Any additional information for the alarm
        """
        logger.info(f"Adding new alarm: type={alarm_type}, source={source}, message={message}")

        # Check if a similar alarm already exists
        for alarm in self.alarms['active']:
            if alarm['source'] == source and alarm['type'] == alarm_type:
                # For battery alarms, check if it's a low battery alarm
                if source == SOURCE_BATTERY and ("low battery" in message.lower() and "low battery" in alarm['message'].lower()):
                    logger.info(f"Similar battery alarm already exists: {alarm['id']}")
                    return alarm['id']
                # For engine alarms, check if it's a high temperature alarm
                elif source == SOURCE_ENGINE and "temperature" in message.lower() and "temperature" in alarm['message'].lower():
                    logger.info(f"Similar engine temperature alarm already exists: {alarm['id']}")
                    return alarm['id']
                # For water alarms, check if it's a low water level alarm
                elif source == SOURCE_WATER and "low water" in message.lower() and "low water" in alarm['message'].lower():
                    logger.info(f"Similar water level alarm already exists: {alarm['id']}")
                    return alarm['id']
                # For fuel alarms, check if it's a low fuel level alarm
                elif source == SOURCE_FUEL and "low fuel" in message.lower() and "low fuel" in alarm['message'].lower():
                    logger.info(f"Similar fuel level alarm already exists: {alarm['id']}")
                    return alarm['id']

        # Generate a unique ID and timestamp
        alarm_id = str(uuid.uuid4())
        timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")

        # Determine category if not provided
        if category is None:
            if alarm_type == ALARM_EMERGENCY or alarm_type == ALARM_CRITICAL:
                category = CATEGORY_A
            elif alarm_type == ALARM_WARNING:
                category = CATEGORY_B
            else:
                category = CATEGORY_C

        # Determine responsibility if not provided
        if responsibility is None:
            responsibility = 'Bridge'

        # Determine alarm group if not provided
        if alarm_group is None:
            alarm_group = source

        # Create the new alarm with maritime standard fields
        new_alarm = {
            'id': alarm_id,
            'type': alarm_type,
            'message': message,
            'source': source,
            'timestamp': timestamp,
            'acknowledged': False,
            'active': True,
            'category': category,
            'responsibility': responsibility,
            'alarm_group': alarm_group,
            'escalation_level': 0,
            'suppression_allowed': not (alarm_type == ALARM_EMERGENCY or alarm_type == ALARM_CRITICAL),
            'shelving_allowed': alarm_type != ALARM_EMERGENCY,
            'creation_time': timestamp,
            'last_update_time': timestamp
        }

        # Add escalation time based on alarm type
        if alarm_type == ALARM_EMERGENCY:
            new_alarm['escalation_time'] = 30  # 30 seconds for emergency
        elif alarm_type == ALARM_CRITICAL:
            new_alarm['escalation_time'] = 60  # 60 seconds for critical
        elif alarm_type == ALARM_WARNING:
            new_alarm['escalation_time'] = 120  # 120 seconds for warnings
        else:
            new_alarm['escalation_time'] = 300  # 300 seconds for others

        # Add visual indication based on alarm type
        if alarm_type == ALARM_EMERGENCY:
            new_alarm['visual_indication'] = 'flashing_red'
        elif alarm_type == ALARM_CRITICAL:
            new_alarm['visual_indication'] = 'red'
        elif alarm_type == ALARM_WARNING:
            new_alarm['visual_indication'] = 'yellow'
        elif alarm_type == ALARM_CAUTION:
            new_alarm['visual_indication'] = 'amber'
        else:
            new_alarm['visual_indication'] = 'blue'

        # Add audio indication based on alarm type
        if alarm_type == ALARM_EMERGENCY:
            new_alarm['audio_indication'] = 'continuous'
        elif alarm_type == ALARM_CRITICAL:
            new_alarm['audio_indication'] = 'intermittent'
        elif alarm_type == ALARM_WARNING:
            new_alarm['audio_indication'] = 'single'
        else:
            new_alarm['audio_indication'] = 'none'

        # Add additional info if provided
        if additional_info:
            new_alarm['additional_info'] = additional_info

        # Add the alarm to the active list
        self.alarms['active'].append(new_alarm)
        self.alarms['last_update'] = timestamp

        # Add to alarm group tracking
        if alarm_group not in self.alarm_groups:
            self.alarm_groups[alarm_group] = []
        self.alarm_groups[alarm_group].append(alarm_id)

        # Save changes
        self.save_alarms()

        # Activate buzzer for critical and emergency alarms
        if alarm_type in [ALARM_EMERGENCY, ALARM_CRITICAL] and self.buzzer_controller:
            try:
                self.buzzer_controller.activate(alarm_type, f"{source}: {message}")
                logger.info(f"Buzzer activated for {alarm_type} alarm: {alarm_id}")
            except Exception as e:
                logger.error(f"Failed to activate buzzer for alarm {alarm_id}: {e}")

        logger.info(f"Added new alarm: {alarm_id}, source: {source}, type: {alarm_type}, category: {category}")

        return alarm_id

    def update_alarm(self, alarm_id, message):
        """Aktualizuje treść istniejącego alarmu"""
        for alarm in self.alarms['active']:
            if alarm['id'] == alarm_id:
                # Aktualizacja treści alarmu
                alarm['message'] = message
                alarm['timestamp'] = datetime.now().strftime("%Y-%m-%d %H:%M:%S")

                # Aktualizacja czasu ostatniej zmiany
                self.alarms['last_update'] = alarm['timestamp']

                # Zapisanie zmian
                self.save_alarms()
                return True

        return False

    def acknowledge_alarm(self, alarm_id, operator=None):
        """
        Acknowledges an alarm according to maritime standards

        Args:
            alarm_id: ID of the alarm to acknowledge
            operator: Name/ID of the operator acknowledging the alarm

        Returns:
            bool: True if the alarm was acknowledged, False otherwise
        """
        for alarm in self.alarms['active']:
            if alarm['id'] == alarm_id:
                # Mark the alarm as acknowledged
                alarm['acknowledged'] = True
                current_time = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
                alarm['acknowledge_time'] = current_time
                alarm['last_update_time'] = current_time

                # Record who acknowledged the alarm if provided
                if operator:
                    alarm['acknowledged_by'] = operator

                # Reset escalation level when acknowledged
                alarm['escalation_level'] = 0

                # Add to acknowledgment history if not present
                if 'acknowledgment_history' not in alarm:
                    alarm['acknowledgment_history'] = []

                # Add this acknowledgment to history
                acknowledgment_entry = {
                    'timestamp': current_time,
                    'operator': operator if operator else 'Unknown'
                }
                alarm['acknowledgment_history'].append(acknowledgment_entry)

                # Update last update time
                self.alarms['last_update'] = current_time

                # Save changes
                self.save_alarms()

                # Check if we should deactivate buzzer
                self._check_buzzer_deactivation()

                # Log the acknowledgment
                logger.info(f"Alarm {alarm_id} acknowledged by {operator if operator else 'Unknown'}")

                # Check if this alarm is part of a group and update group status
                alarm_group = alarm.get('alarm_group')
                if alarm_group and alarm_group in self.alarm_groups:
                    logger.info(f"Updating alarm group status for {alarm_group}")
                    # Check if all alarms in this group are acknowledged
                    all_acknowledged = True
                    for group_alarm_id in self.alarm_groups[alarm_group]:
                        if group_alarm_id != alarm_id:  # Skip the current alarm
                            for a in self.alarms['active']:
                                if a['id'] == group_alarm_id and not a.get('acknowledged', False):
                                    all_acknowledged = False
                                    break

                    if all_acknowledged:
                        logger.info(f"All alarms in group {alarm_group} are now acknowledged")

                return True

        # If we get here, the alarm wasn't found
        logger.warning(f"Attempted to acknowledge non-existent alarm: {alarm_id}")
        return False

    def acknowledge_all_alarms(self, operator=None, category=None, alarm_type=None, source=None):
        """
        Acknowledges all active alarms matching the specified criteria

        Args:
            operator: Name/ID of the operator acknowledging the alarms
            category: Only acknowledge alarms of this category (A, B, C)
            alarm_type: Only acknowledge alarms of this type (emergency, critical, etc.)
            source: Only acknowledge alarms from this source

        Returns:
            int: Number of alarms acknowledged
        """
        acknowledge_time = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        alarm_count = 0

        # Create a list of alarms to acknowledge based on filters
        alarms_to_acknowledge = []
        for alarm in self.alarms['active']:
            # Skip already acknowledged alarms
            if alarm.get('acknowledged', False):
                continue

            # Apply category filter if specified
            if category and alarm.get('category') != category:
                continue

            # Apply type filter if specified
            if alarm_type and alarm['type'] != alarm_type:
                continue

            # Apply source filter if specified
            if source and alarm['source'] != source:
                continue

            alarms_to_acknowledge.append(alarm)

        # Acknowledge each alarm
        for alarm in alarms_to_acknowledge:
            alarm['acknowledged'] = True
            alarm['acknowledge_time'] = acknowledge_time
            alarm['last_update_time'] = acknowledge_time

            # Record who acknowledged the alarm if provided
            if operator:
                alarm['acknowledged_by'] = operator

            # Reset escalation level when acknowledged
            alarm['escalation_level'] = 0

            # Add to acknowledgment history if not present
            if 'acknowledgment_history' not in alarm:
                alarm['acknowledgment_history'] = []

            # Add this acknowledgment to history
            acknowledgment_entry = {
                'timestamp': acknowledge_time,
                'operator': operator if operator else 'Unknown',
                'bulk_acknowledgment': True
            }
            alarm['acknowledgment_history'].append(acknowledgment_entry)

            alarm_count += 1

        # Update last update time
        self.alarms['last_update'] = acknowledge_time

        # Save changes
        self.save_alarms()

        # Log the acknowledgment
        logger.info(f"Acknowledged {alarm_count} alarms by {operator if operator else 'Unknown'}")

        # Update alarm group statuses
        self._update_alarm_group_statuses()

        return alarm_count

    def _update_alarm_group_statuses(self):
        """Updates the status of all alarm groups"""
        for group_name, alarm_ids in self.alarm_groups.items():
            # Check if all alarms in this group are acknowledged
            all_acknowledged = True
            active_count = 0

            for alarm_id in alarm_ids:
                for alarm in self.alarms['active']:
                    if alarm['id'] == alarm_id:
                        active_count += 1
                        if not alarm.get('acknowledged', False):
                            all_acknowledged = False
                            break

            if active_count > 0:
                logger.info(f"Alarm group {group_name}: {active_count} active alarms, all acknowledged: {all_acknowledged}")

        return True

    def deactivate_alarm(self, alarm_id, reason=None, operator=None):
        """
        Deactivates an alarm and moves it to history according to maritime standards

        Args:
            alarm_id: ID of the alarm to deactivate
            reason: Reason for deactivation
            operator: Name/ID of the operator deactivating the alarm

        Returns:
            bool: True if the alarm was deactivated, False otherwise
        """
        logger.info(f"Deactivating alarm {alarm_id}")

        # Sprawdź, czy alarm jest już w historii
        for alarm in self.alarms['history']:
            if alarm['id'] == alarm_id:
                logger.info(f"Alarm {alarm_id} already in history, skipping deactivation")
                return True

        # Utwórz kopię listy aktywnych alarmów, aby uniknąć problemów z modyfikacją podczas iteracji
        active_alarms_copy = list(self.alarms['active'])

        # Check if the alarm exists in the active list
        alarm_found = False
        for alarm in active_alarms_copy:
            if alarm['id'] == alarm_id:
                alarm_found = True
                current_time = datetime.now().strftime("%Y-%m-%d %H:%M:%S")

                # Mark the alarm as inactive
                alarm['active'] = False
                alarm['deactivate_time'] = current_time
                alarm['last_update_time'] = current_time

                # Record deactivation reason and operator if provided
                if reason:
                    alarm['deactivation_reason'] = reason
                if operator:
                    alarm['deactivated_by'] = operator

                # Add to deactivation history if not present
                if 'deactivation_history' not in alarm:
                    alarm['deactivation_history'] = []

                # Add this deactivation to history
                deactivation_entry = {
                    'timestamp': current_time,
                    'reason': reason if reason else 'Condition no longer active',
                    'operator': operator if operator else 'System'
                }
                alarm['deactivation_history'].append(deactivation_entry)

                # Move to history
                self.alarms['history'].append(alarm.copy())

                # Remove from active list - używamy bezpiecznej metody filtrowania
                self.alarms['active'] = [a for a in self.alarms['active'] if a['id'] != alarm_id]
                logger.info(f"Alarm {alarm_id} removed from active list")

                # Update last update time
                self.alarms['last_update'] = current_time

                # Update alarm group tracking
                alarm_group = alarm.get('alarm_group')
                if alarm_group and alarm_group in self.alarm_groups:
                    # Remove this alarm from its group
                    if alarm_id in self.alarm_groups[alarm_group]:
                        self.alarm_groups[alarm_group].remove(alarm_id)
                        logger.info(f"Removed alarm {alarm_id} from group {alarm_group}")

                    # If group is empty, remove it
                    if not self.alarm_groups[alarm_group]:
                        del self.alarm_groups[alarm_group]
                        logger.info(f"Removed empty alarm group {alarm_group}")

                # Save changes
                self.save_alarms()

                # Force update of alarm properties in the application
                try:
                    from kivy.app import App
                    app = App.get_running_app()
                    if app and hasattr(app, 'update_alarm_properties'):
                        app.update_alarm_properties()
                        logger.info("Forced refresh of alarm properties in application")

                        # Refresh alarm screen if currently displayed
                        if hasattr(app, 'root') and app.root and app.root.current == 'alarm':
                            app.root.current_screen.update_alarm_list()
                            logger.info("Forced refresh of alarm list on alarm screen")
                except Exception as e:
                    logger.error(f"Error refreshing alarm properties: {e}")

                return True

        if not alarm_found:
            logger.warning(f"Alarm {alarm_id} not found in active list")

            # Check if the alarm exists in history
            for alarm in self.alarms['history']:
                if alarm['id'] == alarm_id:
                    logger.info(f"Alarm {alarm_id} found in history")
                    return True

            # Check if there are alarms of similar type (e.g., battery alarms)
            if isinstance(alarm_id, str) and "battery" in alarm_id.lower():
                logger.info(f"Looking for similar battery alarms...")
                battery_alarms = [a for a in self.alarms['active'] if a['source'] == SOURCE_BATTERY]
                if battery_alarms:
                    logger.info(f"Found {len(battery_alarms)} similar battery alarms")
                    for a in battery_alarms:
                        logger.info(f"Deactivating similar battery alarm: {a['id']}")
                        # Recursive call for each battery alarm
                        self.deactivate_alarm(a['id'], reason, operator)
                    return True

        # Force saving changes even if alarm was not found
        self.save_alarms()

        # Check if we should deactivate buzzer after any alarm deactivation
        self._check_buzzer_deactivation()

        return False

    def get_active_alarms(self, alarm_types=None):
        """
        Returns a list of active alarms, optionally filtered by types.
        Zoptymalizowana wersja z redukcją operacji I/O i pamięcią podręczną.

        Args:
            alarm_types: Optional list of alarm types to filter by

        Returns:
            list: List of active alarms
        """
        # Aktualizacja statystyk
        _performance_stats['alarm_checks'] += 1

        # Wczytaj alarmy tylko jeśli nie są jeszcze załadowane
        if not hasattr(self, 'alarms') or not self.alarms:
            self.load_alarms()

        # Optymalizacja: Używamy listy aktywnych alarmów bez tworzenia kopii
        # Tylko jeśli będziemy modyfikować listę, utworzymy kopię
        active_alarms = self.alarms.get('active', [])

        # Sprawdzenie, czy są nieaktywne alarmy - wykonujemy tylko co 10 wywołań
        if _performance_stats['alarm_checks'] % 10 == 0:
            # Sprawdzenie, czy są nieaktywne alarmy
            inactive_alarms = [alarm for alarm in active_alarms if not alarm.get('active', True)]

            if inactive_alarms:
                # Teraz tworzymy kopię, ponieważ będziemy modyfikować listę
                active_alarms = [alarm.copy() for alarm in active_alarms if alarm.get('active', True)]

                # Aktualizacja listy aktywnych alarmów
                self.alarms['active'] = [alarm for alarm in self.alarms['active'] if alarm.get('active', True)]

                # Dodanie nieaktywnych alarmów do historii
                for alarm in inactive_alarms:
                    if 'deactivate_time' not in alarm:
                        alarm['deactivate_time'] = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
                    self.alarms['history'].append(alarm.copy())

                # Zapisanie zmian
                self.save_alarms()
            else:
                # Jeśli nie ma nieaktywnych alarmów, tworzymy kopię tylko dla bezpieczeństwa
                active_alarms = [alarm.copy() for alarm in active_alarms]
        else:
            # Tworzymy kopię dla bezpieczeństwa
            active_alarms = [alarm.copy() for alarm in active_alarms]

        # Sprawdzenie, czy są alarmy baterii, które powinny być dezaktywowane - wykonujemy tylko co 5 wywołań
        if _performance_stats['alarm_checks'] % 5 == 0:
            try:
                from kivy.app import App
                app = App.get_running_app()
                if app and hasattr(app, 'battery_level') and app.battery_level >= 20:
                    # Filtrujemy alarmy baterii
                    battery_alarms = [alarm for alarm in active_alarms
                                    if alarm['source'] == SOURCE_BATTERY
                                    and ("low battery" in alarm['message'].lower() or "niski poziom baterii" in alarm['message'].lower())]

                    # Dezaktywujemy alarmy baterii
                    if battery_alarms:
                        for alarm in battery_alarms:
                            self.deactivate_alarm(alarm['id'], f"Battery level returned to normal ({app.battery_level}%)", "System")

                        # Aktualizacja listy aktywnych alarmów
                        active_alarms = [alarm for alarm in active_alarms
                                        if alarm['source'] != SOURCE_BATTERY
                                        or not ("low battery" in alarm['message'].lower() or "niski poziom baterii" in alarm['message'].lower())]
            except Exception:
                pass  # Ciche logowanie błędów

        # Filtrowanie po typach alarmów, jeśli podano
        if alarm_types is not None:
            return [alarm for alarm in active_alarms if alarm['type'] in alarm_types]

        return active_alarms

    def get_alarm_history(self, limit=50, alarm_types=None):
        """Zwraca historię alarmów, opcjonalnie filtrowaną po typach"""
        history = self.alarms['history']

        if alarm_types is not None:
            history = [alarm for alarm in history if alarm['type'] in alarm_types]

        # Sortowanie po czasie (od najnowszych)
        history.sort(key=lambda x: x['timestamp'], reverse=True)

        # Ograniczenie liczby wyników
        return history[:limit]

    def deactivate_all_alarms(self):
        """Dezaktywuje wszystkie alarmy i przenosi je do historii"""
        print("AlarmManager.deactivate_all_alarms: Dezaktywacja wszystkich alarmów")

        deactivate_time = datetime.now().strftime("%Y-%m-%d %H:%M:%S")

        # Kopiowanie listy, aby uniknąć problemów z iteracją podczas usuwania
        active_alarms = self.alarms['active'].copy()
        alarm_count = len(active_alarms)

        for alarm in active_alarms:
            # Oznaczenie alarmu jako nieaktywnego
            alarm['active'] = False
            alarm['deactivate_time'] = deactivate_time

            # Dodanie do historii
            self.alarms['history'].append(alarm.copy())

            print(f"AlarmManager.deactivate_all_alarms: Dezaktywowano alarm {alarm['id']}")

        # Wyczyszczenie listy aktywnych alarmów
        self.alarms['active'] = []

        # Aktualizacja czasu ostatniej zmiany
        self.alarms['last_update'] = deactivate_time

        # Zapisanie zmian
        self.save_alarms()

        # Sprawdzenie, czy lista aktywnych alarmów jest pusta
        if len(self.alarms['active']) > 0:
            print(f"AlarmManager.deactivate_all_alarms: UWAGA! Lista aktywnych alarmów nadal zawiera {len(self.alarms['active'])} elementów!")
        else:
            print("AlarmManager.deactivate_all_alarms: Potwierdzono wyczyszczenie listy aktywnych alarmów")

        print(f"AlarmManager.deactivate_all_alarms: Dezaktywowano {alarm_count} alarmów")

        # Deactivate buzzer when all alarms are cleared
        if self.buzzer_controller:
            try:
                self.buzzer_controller.deactivate("All alarms cleared")
                logger.info("Buzzer deactivated - all alarms cleared")
            except Exception as e:
                logger.error(f"Failed to deactivate buzzer: {e}")

    def deactivate_alarm_if_inactive(self, alarm_id, is_active):
        """Dezaktywuje alarm, jeśli jest nieaktywny"""
        if not is_active:
            return self.deactivate_alarm(alarm_id)
        return False

    def has_active_alarms(self):
        """Sprawdza, czy są aktywne alarmy"""
        # Sprawdzenie, czy są alarmy, które są aktywne
        return any(alarm.get('active', True) for alarm in self.alarms['active'])

    def has_critical_alarms(self):
        """Sprawdza, czy są aktywne alarmy krytyczne"""
        return any(alarm['type'] == ALARM_CRITICAL and alarm.get('active', True) for alarm in self.alarms['active'])

    def clean_up_alarms(self):
        """
        Cleans up inactive alarms and removes duplicates according to maritime standards

        This method performs a thorough cleanup of the alarm system:
        1. Removes inactive alarms from the active list
        2. Removes duplicate alarms
        3. Ensures all alarms have proper maritime fields
        4. Rebuilds alarm groups

        Returns:
            int: Number of alarms removed
        """
        try:
            logger.info("Starting alarm cleanup")

            # Load current alarms with force refresh
            self.load_alarms()

            # Counter for removed alarms
            removed_count = 0
            current_time = datetime.now().strftime("%Y-%m-%d %H:%M:%S")

            # Create a copy of the active alarms list to avoid modification issues
            active_alarms = list(self.alarms['active'])

            # Check for alarms without required fields
            invalid_alarms = []
            for alarm in active_alarms:
                if 'id' not in alarm or 'source' not in alarm or 'type' not in alarm:
                    logger.warning(f"Found invalid alarm without required fields: {alarm}")
                    invalid_alarms.append(alarm)

            # Remove invalid alarms
            if invalid_alarms:
                logger.info(f"Removing {len(invalid_alarms)} invalid alarms")
                for alarm in invalid_alarms:
                    if alarm in self.alarms['active']:
                        self.alarms['active'].remove(alarm)
                        removed_count += 1

            # Remove inactive alarms from active list
            inactive_alarms = [alarm for alarm in self.alarms['active'] if not alarm.get('active', True)]
            if inactive_alarms:
                # Add to history
                for alarm in inactive_alarms:
                    if 'deactivate_time' not in alarm:
                        alarm['deactivate_time'] = current_time
                    if 'deactivation_reason' not in alarm:
                        alarm['deactivation_reason'] = "Inactive alarm cleanup"
                    self.alarms['history'].append(alarm.copy())

                # Remove from active list
                self.alarms['active'] = [alarm for alarm in self.alarms['active'] if alarm.get('active', True)]
                removed_count += len(inactive_alarms)
                logger.info(f"Removed {len(inactive_alarms)} inactive alarms")

            # Group alarms by source and type for duplicate detection
            alarm_groups = {}
            for alarm in self.alarms['active']:
                source = alarm['source']
                alarm_type = alarm['type']
                key = f"{source}_{alarm_type}"

                if key not in alarm_groups:
                    alarm_groups[key] = []
                alarm_groups[key].append(alarm)

            # Check each group for duplicates
            for key, alarms in alarm_groups.items():
                if len(alarms) <= 1:
                    continue

                source = alarms[0]['source']

                # For battery alarms, check for low battery duplicates
                if source == SOURCE_BATTERY:
                    low_battery_alarms = [a for a in alarms if "low battery" in a['message'].lower() or "niski poziom baterii" in a['message'].lower()]
                    if len(low_battery_alarms) > 1:
                        # Keep only the newest low battery alarm
                        low_battery_alarms.sort(key=lambda x: x.get('timestamp', ''), reverse=True)
                        logger.info(f"Found {len(low_battery_alarms)} duplicate low battery alarms, keeping newest")
                        for old_alarm in low_battery_alarms[1:]:
                            # Remove from active list safely
                            self.deactivate_alarm(old_alarm['id'], "Duplicate alarm", "System")
                            removed_count += 1

                # For engine alarms, check for temperature duplicates
                elif source == SOURCE_ENGINE:
                    temp_alarms = [a for a in alarms if "temperature" in a['message'].lower()]
                    if len(temp_alarms) > 1:
                        # Group by alarm type (emergency, critical, warning, caution)
                        type_groups = {}
                        for alarm in temp_alarms:
                            alarm_type = alarm['type']
                            if alarm_type not in type_groups:
                                type_groups[alarm_type] = []
                            type_groups[alarm_type].append(alarm)

                        # Keep only the newest alarm of each type
                        for alarm_type, type_alarms in type_groups.items():
                            if len(type_alarms) > 1:
                                type_alarms.sort(key=lambda x: x.get('timestamp', ''), reverse=True)
                                logger.info(f"Found {len(type_alarms)} duplicate engine temperature {alarm_type} alarms, keeping newest")
                                for old_alarm in type_alarms[1:]:
                                    # Remove safely using deactivate_alarm
                                    self.deactivate_alarm(old_alarm['id'], "Duplicate alarm", "System")
                                    removed_count += 1

                # For water alarms, check for low water level duplicates
                elif source == SOURCE_WATER:
                    low_water_alarms = [a for a in alarms if "low water" in a['message'].lower()]
                    if len(low_water_alarms) > 1:
                        # Group by alarm type
                        type_groups = {}
                        for alarm in low_water_alarms:
                            alarm_type = alarm['type']
                            if alarm_type not in type_groups:
                                type_groups[alarm_type] = []
                            type_groups[alarm_type].append(alarm)

                        # Keep only the newest alarm of each type
                        for alarm_type, type_alarms in type_groups.items():
                            if len(type_alarms) > 1:
                                type_alarms.sort(key=lambda x: x.get('timestamp', ''), reverse=True)
                                logger.info(f"Found {len(type_alarms)} duplicate water level {alarm_type} alarms, keeping newest")
                                for old_alarm in type_alarms[1:]:
                                    # Remove safely using deactivate_alarm
                                    self.deactivate_alarm(old_alarm['id'], "Duplicate alarm", "System")
                                    removed_count += 1

                # For fuel alarms, check for low fuel level duplicates
                elif source == SOURCE_FUEL:
                    low_fuel_alarms = [a for a in alarms if "low fuel" in a['message'].lower()]
                    if len(low_fuel_alarms) > 1:
                        # Group by alarm type
                        type_groups = {}
                        for alarm in low_fuel_alarms:
                            alarm_type = alarm['type']
                            if alarm_type not in type_groups:
                                type_groups[alarm_type] = []
                            type_groups[alarm_type].append(alarm)

                        # Keep only the newest alarm of each type
                        for alarm_type, type_alarms in type_groups.items():
                            if len(type_alarms) > 1:
                                type_alarms.sort(key=lambda x: x.get('timestamp', ''), reverse=True)
                                logger.info(f"Found {len(type_alarms)} duplicate fuel level {alarm_type} alarms, keeping newest")
                                for old_alarm in type_alarms[1:]:
                                    # Remove safely using deactivate_alarm
                                    self.deactivate_alarm(old_alarm['id'], "Duplicate alarm", "System")
                                    removed_count += 1

            # Ensure all remaining alarms have proper maritime fields
            for alarm in self.alarms['active']:
                self._ensure_maritime_fields(alarm)

            # Rebuild alarm groups
            self.alarm_groups = {}
            for alarm in self.alarms['active']:
                if 'alarm_group' in alarm and 'id' in alarm:
                    group = alarm['alarm_group']
                    if group not in self.alarm_groups:
                        self.alarm_groups[group] = []
                    self.alarm_groups[group].append(alarm['id'])

            # Save changes
            if removed_count > 0:
                self.save_alarms()
                logger.info(f"Saved changes after removing {removed_count} alarms")

            return removed_count

        except Exception as e:
            logger.error(f"Error in clean_up_alarms: {e}")
            import traceback
            traceback.print_exc()
            return 0

    def get_system_status(self):
        """
        Returns the overall system status based on alarms according to maritime standards

        Returns:
            str: "EMERGENCY", "CRITICAL", "WARNING", "CAUTION", or "OK"
        """
        if any(alarm['type'] == ALARM_EMERGENCY for alarm in self.alarms['active']):
            return "EMERGENCY"
        elif any(alarm['type'] == ALARM_CRITICAL for alarm in self.alarms['active']):
            return "CRITICAL"
        elif any(alarm['type'] == ALARM_WARNING for alarm in self.alarms['active']):
            return "WARNING"
        elif any(alarm['type'] == ALARM_CAUTION for alarm in self.alarms['active']):
            return "CAUTION"
        else:
            return "OK"

    def shelf_alarm(self, alarm_id, duration_minutes=60, operator=None, reason=None):
        """
        Temporarily shelves (suppresses) an alarm for a specified duration

        Args:
            alarm_id: ID of the alarm to shelf
            duration_minutes: Duration in minutes to shelf the alarm
            operator: Name/ID of the operator shelving the alarm
            reason: Reason for shelving

        Returns:
            bool: True if the alarm was shelved, False otherwise
        """
        for alarm in self.alarms['active']:
            if alarm['id'] == alarm_id:
                # Check if shelving is allowed for this alarm
                if not alarm.get('shelving_allowed', True):
                    logger.warning(f"Cannot shelf alarm {alarm_id}: shelving not allowed for this alarm type")
                    return False

                current_time = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
                expiry_time = datetime.now() + datetime.timedelta(minutes=duration_minutes)
                expiry_time_str = expiry_time.strftime("%Y-%m-%d %H:%M:%S")

                # Create shelving record
                shelving_record = {
                    'id': alarm_id,
                    'alarm': alarm.copy(),
                    'shelved_time': current_time,
                    'expiry_time': expiry_time_str,
                    'duration_minutes': duration_minutes,
                    'operator': operator,
                    'reason': reason
                }

                # Add to shelved alarms list
                if 'shelved' not in self.alarms:
                    self.alarms['shelved'] = []
                self.alarms['shelved'].append(shelving_record)

                # Add to shelving history in the alarm
                if 'shelving_history' not in alarm:
                    alarm['shelving_history'] = []

                shelving_entry = {
                    'timestamp': current_time,
                    'duration_minutes': duration_minutes,
                    'expiry_time': expiry_time_str,
                    'operator': operator if operator else 'Unknown',
                    'reason': reason
                }
                alarm['shelving_history'].append(shelving_entry)

                # Mark the alarm as shelved
                alarm['shelved'] = True
                alarm['shelved_time'] = current_time
                alarm['shelved_expiry'] = expiry_time_str
                alarm['last_update_time'] = current_time

                # Add to shelving tracking
                self.shelved_alarms[alarm_id] = {
                    'expiry_time': expiry_time,
                    'alarm': alarm
                }

                # Save changes
                self.save_alarms()

                logger.info(f"Alarm {alarm_id} shelved for {duration_minutes} minutes by {operator if operator else 'Unknown'}")
                return True

        logger.warning(f"Attempted to shelf non-existent alarm: {alarm_id}")
        return False

    def unshelve_alarm(self, alarm_id, operator=None):
        """
        Removes an alarm from the shelved state

        Args:
            alarm_id: ID of the alarm to unshelve
            operator: Name/ID of the operator unshelving the alarm

        Returns:
            bool: True if the alarm was unshelved, False otherwise
        """
        # Check if the alarm is in the shelved tracking
        if alarm_id in self.shelved_alarms:
            # Remove from shelving tracking
            del self.shelved_alarms[alarm_id]

            # Find the alarm in active alarms
            for alarm in self.alarms['active']:
                if alarm['id'] == alarm_id:
                    current_time = datetime.now().strftime("%Y-%m-%d %H:%M:%S")

                    # Mark the alarm as unshelved
                    alarm['shelved'] = False
                    if 'shelved_time' in alarm:
                        del alarm['shelved_time']
                    if 'shelved_expiry' in alarm:
                        del alarm['shelved_expiry']
                    alarm['last_update_time'] = current_time

                    # Add to shelving history
                    if 'shelving_history' not in alarm:
                        alarm['shelving_history'] = []

                    unshelving_entry = {
                        'timestamp': current_time,
                        'action': 'unshelved',
                        'operator': operator if operator else 'Unknown'
                    }
                    alarm['shelving_history'].append(unshelving_entry)

                    # Remove from shelved alarms list
                    if 'shelved' in self.alarms:
                        self.alarms['shelved'] = [s for s in self.alarms.get('shelved', []) if s['id'] != alarm_id]

                    # Save changes
                    self.save_alarms()

                    logger.info(f"Alarm {alarm_id} unshelved by {operator if operator else 'Unknown'}")
                    return True

            # If we get here, the alarm is in shelving tracking but not in active alarms
            logger.warning(f"Alarm {alarm_id} is in shelving tracking but not in active alarms")

            # Clean up shelved alarms list
            if 'shelved' in self.alarms:
                self.alarms['shelved'] = [s for s in self.alarms.get('shelved', []) if s['id'] != alarm_id]
                self.save_alarms()

            return True

        logger.warning(f"Attempted to unshelve non-shelved alarm: {alarm_id}")
        return False

    def check_shelved_alarms(self):
        """
        Checks all shelved alarms and unshelves any that have expired

        Returns:
            int: Number of alarms that were unshelved
        """
        current_time = datetime.now()
        alarms_to_unshelve = []

        # Find expired shelved alarms
        for alarm_id, shelving_info in list(self.shelved_alarms.items()):
            if current_time >= shelving_info['expiry_time']:
                alarms_to_unshelve.append(alarm_id)

        # Unshelve expired alarms
        unshelved_count = 0
        for alarm_id in alarms_to_unshelve:
            if self.unshelve_alarm(alarm_id, 'System (expiry)'):
                unshelved_count += 1

        if unshelved_count > 0:
            logger.info(f"Unshelved {unshelved_count} expired shelved alarms")

        return unshelved_count

    def suppress_alarm(self, alarm_id, operator=None, reason=None):
        """
        Permanently suppresses an alarm

        Args:
            alarm_id: ID of the alarm to suppress
            operator: Name/ID of the operator suppressing the alarm
            reason: Reason for suppression

        Returns:
            bool: True if the alarm was suppressed, False otherwise
        """
        for alarm in self.alarms['active']:
            if alarm['id'] == alarm_id:
                # Check if suppression is allowed for this alarm
                if not alarm.get('suppression_allowed', False):
                    logger.warning(f"Cannot suppress alarm {alarm_id}: suppression not allowed for this alarm type")
                    return False

                current_time = datetime.now().strftime("%Y-%m-%d %H:%M:%S")

                # Create suppression record
                suppression_record = {
                    'id': alarm_id,
                    'alarm': alarm.copy(),
                    'suppressed_time': current_time,
                    'operator': operator,
                    'reason': reason
                }

                # Add to suppressed alarms list
                if 'suppressed' not in self.alarms:
                    self.alarms['suppressed'] = []
                self.alarms['suppressed'].append(suppression_record)

                # Add to suppression history in the alarm
                if 'suppression_history' not in alarm:
                    alarm['suppression_history'] = []

                suppression_entry = {
                    'timestamp': current_time,
                    'operator': operator if operator else 'Unknown',
                    'reason': reason
                }
                alarm['suppression_history'].append(suppression_entry)

                # Add to suppression rules
                source = alarm['source']
                message_key = self._get_message_key(alarm['message'])

                if source not in self.suppression_rules:
                    self.suppression_rules[source] = {}
                self.suppression_rules[source][message_key] = {
                    'suppressed_time': current_time,
                    'operator': operator,
                    'reason': reason
                }

                # Deactivate the alarm
                self.deactivate_alarm(alarm_id, f"Suppressed by {operator if operator else 'Unknown'}", operator)

                logger.info(f"Alarm {alarm_id} suppressed by {operator if operator else 'Unknown'}")
                return True

        logger.warning(f"Attempted to suppress non-existent alarm: {alarm_id}")
        return False

    def _get_message_key(self, message):
        """
        Creates a key from an alarm message for suppression matching

        Args:
            message: The alarm message

        Returns:
            str: A key derived from the message for matching similar messages
        """
        # Convert to lowercase and remove punctuation
        message = message.lower()
        for char in '.,!?;:()[]{}':
            message = message.replace(char, '')

        # Split into words and sort
        words = message.split()

        # Keep only significant words (longer than 3 characters)
        significant_words = [word for word in words if len(word) > 3]

        # If no significant words, use all words
        if not significant_words:
            significant_words = words

        # Sort and join with underscores
        return '_'.join(sorted(significant_words))

    def check_suppression_rules(self, alarm_type, message, source):
        """
        Checks if an alarm should be suppressed based on suppression rules

        Args:
            alarm_type: Type of the alarm
            message: The alarm message
            source: Source of the alarm

        Returns:
            bool: True if the alarm should be suppressed, False otherwise
        """
        # Emergency and critical alarms cannot be suppressed
        if alarm_type == ALARM_EMERGENCY or alarm_type == ALARM_CRITICAL:
            return False

        # Check if source has any suppression rules
        if source not in self.suppression_rules:
            return False

        # Get message key
        message_key = self._get_message_key(message)

        # Check for exact match
        if message_key in self.suppression_rules[source]:
            logger.info(f"Alarm suppressed by rule: {source}/{message_key}")
            return True

        # Check for partial matches
        for rule_key, _ in self.suppression_rules[source].items():
            # If the rule key is a subset of the message key or vice versa
            if rule_key in message_key or message_key in rule_key:
                logger.info(f"Alarm suppressed by partial match rule: {source}/{rule_key}")
                return True

        return False

    def escalate_alarms(self):
        """
        Escalates unacknowledged alarms based on their escalation time

        Returns:
            list: List of alarms that were escalated
        """
        current_time = datetime.now()
        escalated_alarms = []

        for alarm in self.alarms['active']:
            # Skip acknowledged alarms
            if alarm.get('acknowledged', False):
                continue

            # Skip shelved alarms
            if alarm.get('shelved', False):
                continue

            # Get the alarm's creation or last escalation time
            if 'last_escalation_time' in alarm:
                last_time_str = alarm['last_escalation_time']
            else:
                last_time_str = alarm['timestamp']

            last_time = datetime.strptime(last_time_str, "%Y-%m-%d %H:%M:%S")

            # Get escalation time in seconds
            escalation_time = alarm.get('escalation_time', 300)  # Default: 5 minutes

            # Check if it's time to escalate
            if (current_time - last_time).total_seconds() >= escalation_time:
                # Escalate the alarm
                alarm['escalation_level'] = alarm.get('escalation_level', 0) + 1
                alarm['last_escalation_time'] = current_time.strftime("%Y-%m-%d %H:%M:%S")
                alarm['last_update_time'] = current_time.strftime("%Y-%m-%d %H:%M:%S")

                # Add to escalation history
                if 'escalation_history' not in alarm:
                    alarm['escalation_history'] = []

                escalation_entry = {
                    'timestamp': current_time.strftime("%Y-%m-%d %H:%M:%S"),
                    'level': alarm['escalation_level']
                }
                alarm['escalation_history'].append(escalation_entry)

                # If this is a critical alarm and it's been escalated multiple times,
                # consider upgrading it to emergency
                if alarm['type'] == ALARM_CRITICAL and alarm['escalation_level'] >= 3:
                    alarm['type'] = ALARM_EMERGENCY
                    alarm['category'] = CATEGORY_A
                    alarm['visual_indication'] = 'flashing_red'
                    alarm['audio_indication'] = 'continuous'
                    logger.warning(f"Critical alarm {alarm['id']} escalated to EMERGENCY after {alarm['escalation_level']} escalations")

                # If this is a warning alarm and it's been escalated multiple times,
                # consider upgrading it to critical
                elif alarm['type'] == ALARM_WARNING and alarm['escalation_level'] >= 2:
                    alarm['type'] = ALARM_CRITICAL
                    alarm['category'] = CATEGORY_A
                    alarm['visual_indication'] = 'red'
                    alarm['audio_indication'] = 'intermittent'
                    logger.warning(f"Warning alarm {alarm['id']} escalated to CRITICAL after {alarm['escalation_level']} escalations")

                escalated_alarms.append(alarm)
                logger.info(f"Escalated alarm {alarm['id']} to level {alarm['escalation_level']}")

        # Save changes if any alarms were escalated
        if escalated_alarms:
            self.save_alarms()

        return escalated_alarms

    # Metody pomocnicze do tworzenia typowych alarmów
    def add_battery_alarm(self, message, alarm_type=ALARM_WARNING):
        return self.add_alarm(alarm_type, message, SOURCE_BATTERY)

    def update_battery_alarm(self, alarm_id, message):
        """Aktualizuje treść alarmu baterii"""
        return self.update_alarm(alarm_id, message)

    def add_climate_alarm(self, message, alarm_type=ALARM_WARNING):
        return self.add_alarm(alarm_type, message, SOURCE_CLIMATE)

    def add_engine_alarm(self, message, alarm_type=ALARM_WARNING):
        return self.add_alarm(alarm_type, message, SOURCE_ENGINE)

    def add_water_alarm(self, message, alarm_type=ALARM_WARNING):
        return self.add_alarm(alarm_type, message, SOURCE_WATER)

    def add_fuel_alarm(self, message, alarm_type=ALARM_WARNING):
        return self.add_alarm(alarm_type, message, SOURCE_FUEL)

    def add_system_alarm(self, message, alarm_type=ALARM_WARNING):
        return self.add_alarm(alarm_type, message, SOURCE_SYSTEM)

    def _check_buzzer_deactivation(self):
        """
        Check if buzzer should be deactivated based on current alarm state.
        Buzzer is deactivated when:
        1. All critical/emergency alarms are acknowledged, OR
        2. No critical/emergency alarms are active
        """
        if not self.buzzer_controller or not self.buzzer_controller.is_buzzer_active():
            return

        # Get all active critical and emergency alarms
        critical_emergency_alarms = [
            alarm for alarm in self.alarms['active']
            if alarm['type'] in [ALARM_EMERGENCY, ALARM_CRITICAL] and alarm.get('active', True)
        ]

        # Check if all critical/emergency alarms are acknowledged
        all_acknowledged = True
        for alarm in critical_emergency_alarms:
            if not alarm.get('acknowledged', False):
                all_acknowledged = False
                break

        # Deactivate buzzer if no critical/emergency alarms or all are acknowledged
        if not critical_emergency_alarms or all_acknowledged:
            try:
                reason = "All critical alarms acknowledged" if all_acknowledged else "No critical alarms active"
                self.buzzer_controller.deactivate(reason)
                logger.info(f"Buzzer deactivated: {reason}")
            except Exception as e:
                logger.error(f"Failed to deactivate buzzer: {e}")

    def acknowledge_all_critical_alarms(self, operator=None):
        """
        Acknowledge all critical and emergency alarms and deactivate buzzer.
        This is the main method called by the "CONFIRM ALARM" button.

        Args:
            operator (str): Name/ID of the operator acknowledging alarms

        Returns:
            int: Number of alarms acknowledged
        """
        # Acknowledge all critical and emergency alarms
        acknowledged_count = self.acknowledge_all_alarms(
            operator=operator,
            alarm_type=None  # Will filter in the method
        )

        # Filter to only count critical and emergency alarms
        critical_emergency_count = 0
        for alarm in self.alarms['active']:
            if (alarm['type'] in [ALARM_EMERGENCY, ALARM_CRITICAL] and
                alarm.get('acknowledged', False)):
                critical_emergency_count += 1

        # Force buzzer deactivation
        if self.buzzer_controller:
            try:
                self.buzzer_controller.deactivate(f"Critical alarms acknowledged by {operator or 'operator'}")
                logger.info(f"Buzzer deactivated by operator: {operator or 'Unknown'}")
            except Exception as e:
                logger.error(f"Failed to deactivate buzzer: {e}")

        logger.info(f"Acknowledged {critical_emergency_count} critical/emergency alarms")
        return critical_emergency_count

    def get_buzzer_status(self):
        """Get current buzzer status."""
        if not self.buzzer_controller:
            return {
                'available': False,
                'active': False,
                'stats': {}
            }

        return {
            'available': True,
            'active': self.buzzer_controller.is_buzzer_active(),
            'stats': self.buzzer_controller.get_stats()
        }

    def force_buzzer_deactivation(self, operator=None):
        """Force deactivation of buzzer (emergency override)."""
        if self.buzzer_controller:
            try:
                reason = f"Emergency override by {operator or 'operator'}"
                self.buzzer_controller.deactivate(reason)
                logger.warning(f"Buzzer force deactivated: {reason}")
                return True
            except Exception as e:
                logger.error(f"Failed to force deactivate buzzer: {e}")
                return False
        return False

# Metoda do dodawania testowego alarmu
def add_test_alarm():
    """Dodaje testowy alarm do systemu"""
    print("=== DODAWANIE TESTOWEGO ALARMU ===")

    # Inicjalizacja menedżera alarmów
    alarm_mgr = get_instance()

    # Dodanie testowego alarmu krytycznego
    alarm_id = alarm_mgr.add_alarm(
        ALARM_CRITICAL,
        "This is a critical test alarm. Please acknowledge.",
        SOURCE_SYSTEM
    )

    print(f"Dodano testowy alarm krytyczny o ID: {alarm_id}")
    print("=== ZAKOŃCZENIE DODAWANIA TESTOWEGO ALARMU ===")

    return alarm_id

# Singleton instance
_instance = None

def get_instance():
    global _instance
    if _instance is None:
        _instance = AlarmManager()
    return _instance
