#!/bin/bash
# TridentOS Startup Script for Raspberry Pi 5
# Automatycznie konfiguruje środowisko i uruchamia TridentOS
# Autor: TridentOS Team
# Data: 2025-06-14

set -e  # Exit on any error

# Kolory dla output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Funkcja logowania
log() {
    echo -e "${GREEN}[$(date +'%Y-%m-%d %H:%M:%S')] $1${NC}"
}

warn() {
    echo -e "${YELLOW}[$(date +'%Y-%m-%d %H:%M:%S')] WARNING: $1${NC}"
}

error() {
    echo -e "${RED}[$(date +'%Y-%m-%d %H:%M:%S')] ERROR: $1${NC}"
}

info() {
    echo -e "${BLUE}[$(date +'%Y-%m-%d %H:%M:%S')] INFO: $1${NC}"
}

# Banner
echo -e "${BLUE}"
echo "╔══════════════════════════════════════════════════════════════╗"
echo "║                    TridentOS Marine Control                  ║"
echo "║                  Raspberry Pi 5 Startup Script              ║"
echo "║                        Version 2.0                          ║"
echo "╚══════════════════════════════════════════════════════════════╝"
echo -e "${NC}"

# Sprawdź czy uruchomiono na Raspberry Pi
check_raspberry_pi() {
    log "Sprawdzanie platformy..."
    
    if [ -f /proc/cpuinfo ]; then
        if grep -q "BCM" /proc/cpuinfo && grep -q "ARM" /proc/cpuinfo; then
            log "✓ Wykryto Raspberry Pi"
            return 0
        fi
    fi
    
    warn "Nie wykryto Raspberry Pi - uruchamianie w trybie symulacji"
    return 1
}

# Sprawdź wymagania systemowe
check_requirements() {
    log "Sprawdzanie wymagań systemowych..."
    
    # Sprawdź Python
    if command -v python3 &> /dev/null; then
        PYTHON_VERSION=$(python3 --version | cut -d' ' -f2)
        log "✓ Python $PYTHON_VERSION"
    else
        error "Python3 nie jest zainstalowany!"
        exit 1
    fi
    
    # Sprawdź pip
    if command -v pip3 &> /dev/null; then
        log "✓ pip3 dostępny"
    else
        error "pip3 nie jest zainstalowany!"
        exit 1
    fi
    
    # Sprawdź X11 (dla GUI)
    if [ -n "$DISPLAY" ]; then
        log "✓ X11 Display: $DISPLAY"
    else
        warn "Brak DISPLAY - ustawianie :0"
        export DISPLAY=:0
    fi
}

# Konfiguracja środowiska
setup_environment() {
    log "Konfiguracja środowiska..."
    
    # Przejdź do katalogu TridentOS
    SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
    cd "$SCRIPT_DIR"
    log "✓ Katalog roboczy: $SCRIPT_DIR"
    
    # Sprawdź czy istnieją wymagane pliki
    REQUIRED_FILES=("main.py" "translations.py" "ui/settings.kv" "widgets/virtual_keyboard.py")
    for file in "${REQUIRED_FILES[@]}"; do
        if [ -f "$file" ]; then
            log "✓ Znaleziono: $file"
        else
            error "Brak wymaganego pliku: $file"
            exit 1
        fi
    done
    
    # Utwórz katalogi jeśli nie istnieją
    mkdir -p logs
    mkdir -p backups
    
    # Ustawienia środowiska dla Kivy
    export KIVY_WINDOW_ICON=""
    export KIVY_NO_CONSOLELOG=1
    export KIVY_LOG_LEVEL=info
    
    # Ustawienia dla touchscreen
    export KIVY_WINDOW_FULLSCREEN=1
    export KIVY_WINDOW_BORDERLESS=1
    
    log "✓ Środowisko skonfigurowane"
}

# Instalacja/aktualizacja zależności
install_dependencies() {
    log "Sprawdzanie zależności Python..."
    
    # Lista wymaganych pakietów
    REQUIRED_PACKAGES=(
        "kivy>=2.3.0"
        "kivymd>=1.1.0"
    )
    
    # Dodaj pakiety specyficzne dla Raspberry Pi
    if check_raspberry_pi; then
        REQUIRED_PACKAGES+=("RPi.GPIO" "gpiozero")
    fi
    
    # Sprawdź i zainstaluj pakiety
    for package in "${REQUIRED_PACKAGES[@]}"; do
        PACKAGE_NAME=$(echo "$package" | cut -d'>' -f1 | cut -d'=' -f1)
        if python3 -c "import $PACKAGE_NAME" 2>/dev/null; then
            log "✓ $PACKAGE_NAME już zainstalowany"
        else
            log "Instalowanie $package..."
            pip3 install "$package" --user --quiet
            if [ $? -eq 0 ]; then
                log "✓ Zainstalowano $package"
            else
                error "Nie udało się zainstalować $package"
                exit 1
            fi
        fi
    done
}

# Konfiguracja sprzętu Raspberry Pi
setup_hardware() {
    if ! check_raspberry_pi; then
        warn "Pomijanie konfiguracji sprzętu (nie na Raspberry Pi)"
        return 0
    fi
    
    log "Konfiguracja sprzętu Raspberry Pi..."
    
    # Sprawdź kontrolę jasności
    if [ -f "/sys/class/backlight/rpi_backlight/brightness" ]; then
        log "✓ Kontrola jasności dostępna"
        # Ustaw domyślną jasność na 80%
        if [ -w "/sys/class/backlight/rpi_backlight/brightness" ]; then
            echo 204 > /sys/class/backlight/rpi_backlight/brightness 2>/dev/null || true
        else
            warn "Brak uprawnień do kontroli jasności - może wymagać sudo"
        fi
    else
        warn "Kontrola jasności niedostępna"
    fi
    
    # Sprawdź WiFi
    if command -v iwconfig &> /dev/null; then
        if iwconfig 2>/dev/null | grep -q "wlan0"; then
            log "✓ Interfejs WiFi wlan0 dostępny"
        else
            warn "Interfejs WiFi wlan0 nie znaleziony"
        fi
    else
        warn "iwconfig nie jest dostępny"
    fi
    
    # Sprawdź GPIO
    if [ -d "/sys/class/gpio" ]; then
        log "✓ GPIO dostępne"
    else
        warn "GPIO niedostępne"
    fi
    
    # Wyłącz wygaszacz ekranu
    if command -v xset &> /dev/null; then
        xset s off 2>/dev/null || true
        xset -dpms 2>/dev/null || true
        xset s noblank 2>/dev/null || true
        log "✓ Wygaszacz ekranu wyłączony"
    fi
    
    # Ukryj kursor myszy
    if command -v unclutter &> /dev/null; then
        unclutter -idle 1 -root &
        log "✓ Kursor myszy ukryty"
    else
        warn "unclutter nie jest zainstalowany - kursor może być widoczny"
    fi
}

# Backup ustawień
backup_settings() {
    log "Tworzenie kopii zapasowej ustawień..."
    
    if [ -f "settings.json" ]; then
        BACKUP_FILE="backups/settings_$(date +%Y%m%d_%H%M%S).json"
        cp "settings.json" "$BACKUP_FILE"
        log "✓ Kopia zapasowa: $BACKUP_FILE"
        
        # Zachowaj tylko 10 najnowszych kopii
        ls -t backups/settings_*.json 2>/dev/null | tail -n +11 | xargs rm -f 2>/dev/null || true
    fi
}

# Sprawdź aktualizacje
check_updates() {
    log "Sprawdzanie aktualizacji..."
    
    # Sprawdź czy istnieje plik wersji
    if [ -f "version.txt" ]; then
        CURRENT_VERSION=$(cat version.txt)
        log "✓ Aktualna wersja: $CURRENT_VERSION"
    else
        warn "Brak pliku wersji"
    fi
    
    # Tutaj można dodać sprawdzanie aktualizacji z serwera
    # Na razie tylko informacja
    info "Sprawdzanie aktualizacji zostanie dodane w przyszłej wersji"
}

# Uruchom TridentOS
start_tridentos() {
    log "Uruchamianie TridentOS..."
    
    # Utwórz plik logu
    LOG_FILE="logs/tridentos_$(date +%Y%m%d_%H%M%S).log"
    
    # Sprawdź czy main.py istnieje
    if [ ! -f "main.py" ]; then
        error "Plik main.py nie istnieje!"
        exit 1
    fi
    
    log "✓ Uruchamianie aplikacji..."
    log "✓ Logi zapisywane do: $LOG_FILE"
    
    # Uruchom aplikację z przekierowaniem logów
    python3 main.py 2>&1 | tee "$LOG_FILE"
    
    # Sprawdź kod wyjścia
    EXIT_CODE=${PIPESTATUS[0]}
    if [ $EXIT_CODE -eq 0 ]; then
        log "✓ TridentOS zakończył działanie normalnie"
    else
        error "TridentOS zakończył działanie z błędem (kod: $EXIT_CODE)"
        
        # Pokaż ostatnie linie logu w przypadku błędu
        echo -e "\n${RED}Ostatnie linie logu:${NC}"
        tail -20 "$LOG_FILE"
    fi
    
    return $EXIT_CODE
}

# Funkcja czyszczenia przy wyjściu
cleanup() {
    log "Czyszczenie zasobów..."
    
    # Zabij procesy unclutter
    pkill unclutter 2>/dev/null || true
    
    # Przywróć ustawienia ekranu
    if command -v xset &> /dev/null; then
        xset s on 2>/dev/null || true
        xset +dpms 2>/dev/null || true
    fi
    
    log "✓ Czyszczenie zakończone"
}

# Funkcja obsługi sygnałów
handle_signal() {
    warn "Otrzymano sygnał przerwania"
    cleanup
    exit 130
}

# Główna funkcja
main() {
    # Ustaw obsługę sygnałów
    trap handle_signal SIGINT SIGTERM
    
    log "Rozpoczynanie uruchamiania TridentOS..."
    
    # Wykonaj wszystkie kroki konfiguracji
    check_requirements
    setup_environment
    install_dependencies
    setup_hardware
    backup_settings
    check_updates
    
    log "✓ Konfiguracja zakończona pomyślnie"
    log "🚀 Uruchamianie TridentOS Marine Control System..."
    
    # Uruchom aplikację
    start_tridentos
    EXIT_CODE=$?
    
    # Czyszczenie
    cleanup
    
    if [ $EXIT_CODE -eq 0 ]; then
        log "🎉 TridentOS zakończył działanie pomyślnie"
    else
        error "❌ TridentOS zakończył działanie z błędami"
    fi
    
    exit $EXIT_CODE
}

# Sprawdź argumenty wiersza poleceń
case "${1:-}" in
    --help|-h)
        echo "TridentOS Startup Script"
        echo "Użycie: $0 [opcje]"
        echo ""
        echo "Opcje:"
        echo "  --help, -h     Pokaż tę pomoc"
        echo "  --version, -v  Pokaż wersję"
        echo "  --test         Uruchom w trybie testowym"
        echo "  --setup        Tylko konfiguracja, bez uruchamiania"
        echo ""
        exit 0
        ;;
    --version|-v)
        echo "TridentOS Startup Script v2.0"
        echo "Kompatybilny z Raspberry Pi 5"
        exit 0
        ;;
    --test)
        log "Tryb testowy - sprawdzanie konfiguracji..."
        check_requirements
        setup_environment
        log "✓ Test konfiguracji zakończony pomyślnie"
        exit 0
        ;;
    --setup)
        log "Tryb konfiguracji - bez uruchamiania aplikacji..."
        check_requirements
        setup_environment
        install_dependencies
        setup_hardware
        log "✓ Konfiguracja zakończona"
        exit 0
        ;;
    "")
        # Brak argumentów - normalne uruchomienie
        main
        ;;
    *)
        error "Nieznany argument: $1"
        echo "Użyj --help aby zobaczyć dostępne opcje"
        exit 1
        ;;
esac
