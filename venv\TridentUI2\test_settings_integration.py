#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Integration test for TridentOS enhanced settings
Tests the complete settings system with UI components
"""

import os
import sys
import json
import time

# Add current directory to path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_settings_screen_creation():
    """Test SettingsScreen creation and initialization"""
    print("=== Testing SettingsScreen Creation ===")
    try:
        # Import required modules
        from main import SettingsScreen
        from kivy.app import App
        
        # Create a minimal app for testing
        class TestApp(App):
            def build(self):
                return SettingsScreen()
        
        # Create settings screen
        settings_screen = SettingsScreen()
        print(f"✓ SettingsScreen created: {type(settings_screen)}")
        
        # Test properties
        assert hasattr(settings_screen, 'language')
        assert hasattr(settings_screen, 'brightness')
        assert hasattr(settings_screen, 'theme_mode')
        assert hasattr(settings_screen, 'pin_code')
        assert hasattr(settings_screen, 'wifi_enabled')
        print("✓ All required properties present")
        
        # Test methods
        assert hasattr(settings_screen, 'set_language')
        assert hasattr(settings_screen, 'set_brightness')
        assert hasattr(settings_screen, 'set_pin_code')
        assert hasattr(settings_screen, 'scan_wifi_networks')
        assert hasattr(settings_screen, 'show_virtual_keyboard')
        print("✓ All required methods present")
        
        return True
    except Exception as e:
        print(f"✗ SettingsScreen creation error: {e}")
        return False

def test_pin_code_functionality():
    """Test PIN code setting and verification"""
    print("\n=== Testing PIN Code Functionality ===")
    try:
        from main import SettingsScreen
        
        settings = SettingsScreen()
        
        # Test valid PIN
        result = settings.set_pin_code("1234")
        assert result == True
        assert settings.pin_code == "1234"
        assert settings.pin_enabled == True
        print("✓ Valid PIN setting works")
        
        # Test PIN verification
        assert settings.verify_pin("1234") == True
        assert settings.verify_pin("0000") == False
        print("✓ PIN verification works")
        
        # Test invalid PIN
        result = settings.set_pin_code("123")  # Too short
        assert result == False
        print("✓ Invalid PIN rejection works")
        
        result = settings.set_pin_code("abcd")  # Non-numeric
        assert result == False
        print("✓ Non-numeric PIN rejection works")
        
        return True
    except Exception as e:
        print(f"✗ PIN code functionality error: {e}")
        return False

def test_language_switching():
    """Test language switching functionality"""
    print("\n=== Testing Language Switching ===")
    try:
        from main import SettingsScreen
        from translations import get_available_languages
        
        settings = SettingsScreen()
        languages = get_available_languages()
        
        # Test each language
        for lang in languages:
            settings.set_language(lang)
            assert settings.language == lang
            print(f"✓ Language switched to: {lang}")
        
        # Test language persistence
        settings.save_settings()
        settings.load_settings()
        assert settings.language in languages
        print("✓ Language persistence works")
        
        return True
    except Exception as e:
        print(f"✗ Language switching error: {e}")
        return False

def test_wifi_functionality():
    """Test WiFi scanning and connection simulation"""
    print("\n=== Testing WiFi Functionality ===")
    try:
        from main import SettingsScreen
        
        settings = SettingsScreen()
        
        # Test WiFi scanning
        settings.scan_wifi_networks()
        assert len(settings.available_networks) > 0
        print(f"✓ WiFi scan found {len(settings.available_networks)} networks")
        
        # Test network selection
        if settings.available_networks:
            test_network = settings.available_networks[0]
            settings.select_network(test_network)
            assert settings.selected_network == test_network
            print(f"✓ Network selection works: {test_network}")
            
            # Test connection simulation
            result = settings.connect_to_wifi(test_network, "test_password")
            assert result == True
            assert settings.connected_network == test_network
            print(f"✓ WiFi connection simulation works")
        
        return True
    except Exception as e:
        print(f"✗ WiFi functionality error: {e}")
        return False

def test_brightness_control():
    """Test brightness control functionality"""
    print("\n=== Testing Brightness Control ===")
    try:
        from main import SettingsScreen
        
        settings = SettingsScreen()
        
        # Test brightness setting
        test_values = [25, 50, 75, 100]
        for brightness in test_values:
            settings.set_brightness(brightness)
            assert settings.brightness == brightness
            print(f"✓ Brightness set to: {brightness}%")
        
        # Test brightness persistence
        settings.save_settings()
        settings.load_settings()
        assert settings.brightness in test_values
        print("✓ Brightness persistence works")
        
        return True
    except Exception as e:
        print(f"✗ Brightness control error: {e}")
        return False

def test_theme_switching():
    """Test theme switching functionality"""
    print("\n=== Testing Theme Switching ===")
    try:
        from main import SettingsScreen
        
        settings = SettingsScreen()
        
        # Test theme switching
        original_theme = settings.theme_mode
        settings.toggle_theme()
        new_theme = settings.theme_mode
        
        assert new_theme != original_theme
        assert new_theme in ["Day", "Night"]
        print(f"✓ Theme switched from {original_theme} to {new_theme}")
        
        # Test theme persistence
        settings.save_settings()
        settings.load_settings()
        assert settings.theme_mode == new_theme
        print("✓ Theme persistence works")
        
        return True
    except Exception as e:
        print(f"✗ Theme switching error: {e}")
        return False

def test_keyboard_integration():
    """Test virtual keyboard integration"""
    print("\n=== Testing Keyboard Integration ===")
    try:
        from main import SettingsScreen
        from widgets.virtual_keyboard import VirtualKeyboard
        
        settings = SettingsScreen()
        
        # Test keyboard showing/hiding
        settings.show_virtual_keyboard(is_password=True, is_numeric=True)
        assert settings.show_keyboard == True
        print("✓ Virtual keyboard can be shown")
        
        settings.hide_virtual_keyboard()
        assert settings.show_keyboard == False
        print("✓ Virtual keyboard can be hidden")
        
        # Test keyboard creation with different modes
        keyboard_text = VirtualKeyboard(is_password=False, is_numeric=False)
        keyboard_password = VirtualKeyboard(is_password=True, is_numeric=False)
        keyboard_numeric = VirtualKeyboard(is_password=False, is_numeric=True)
        
        assert keyboard_text.is_password == False
        assert keyboard_password.is_password == True
        assert keyboard_numeric.is_numeric == True
        print("✓ Different keyboard modes work")
        
        return True
    except Exception as e:
        print(f"✗ Keyboard integration error: {e}")
        return False

def test_complete_workflow():
    """Test complete settings workflow"""
    print("\n=== Testing Complete Workflow ===")
    try:
        from main import SettingsScreen
        
        settings = SettingsScreen()
        
        # Complete workflow test
        print("Starting complete workflow test...")
        
        # 1. Set language
        settings.set_language("Polski")
        assert settings.language == "Polski"
        
        # 2. Set brightness
        settings.set_brightness(75)
        assert settings.brightness == 75
        
        # 3. Set theme
        settings.toggle_theme()
        
        # 4. Set PIN
        result = settings.set_pin_code("5678")
        assert result == True
        assert settings.pin_code == "5678"
        
        # 5. Scan WiFi
        settings.scan_wifi_networks()
        assert len(settings.available_networks) > 0
        
        # 6. Save all settings
        settings.save_settings()
        
        # 7. Create new instance and load settings
        new_settings = SettingsScreen()
        new_settings.load_settings()
        
        # 8. Verify persistence
        assert new_settings.language == "Polski"
        assert new_settings.brightness == 75
        assert new_settings.pin_code == "5678"
        assert new_settings.pin_enabled == True
        
        print("✓ Complete workflow test passed")
        return True
        
    except Exception as e:
        print(f"✗ Complete workflow error: {e}")
        return False

def main():
    """Run all integration tests"""
    print("TridentOS Settings Integration Test Suite")
    print("=" * 50)
    
    tests = [
        test_settings_screen_creation,
        test_pin_code_functionality,
        test_language_switching,
        test_wifi_functionality,
        test_brightness_control,
        test_theme_switching,
        test_keyboard_integration,
        test_complete_workflow
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        try:
            if test():
                passed += 1
        except Exception as e:
            print(f"✗ Test failed with exception: {e}")
    
    print("\n" + "=" * 50)
    print(f"Integration Test Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All integration tests passed! Settings system is fully functional.")
        return 0
    else:
        print("⚠️  Some integration tests failed. Check the implementation.")
        return 1

if __name__ == "__main__":
    exit(main())
