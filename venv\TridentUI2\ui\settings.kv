#:kivy 2.0.0

<SettingsScreen>:
    canvas.before:
        Color:
            rgba: 0.05, 0.1, 0.15, 1  # Dark navy background
        Rectangle:
            pos: self.pos
            size: self.size

    FloatLayout:
        # Header with back button, title, and clock
        BoxLayout:
            orientation: 'horizontal'
            size_hint: 1, 0.1
            pos_hint: {'top': 1}
            padding: [12, 12, 12, 0]
            
            # Back button
            Button:
                size_hint: None, None
                size: 40, 40
                background_normal: ''
                background_color: 0, 0, 0, 0
                on_press: root.go_to_home()
                Image:
                    source: 'icons/back.png'
                    size_hint: None, None
                    size: 28, 28
                    center_x: self.parent.center_x
                    center_y: self.parent.center_y
                    allow_stretch: True
                    keep_ratio: True
            
            # Title and clock in center
            BoxLayout:
                orientation: 'vertical'
                size_hint_x: 0.8
                
                Label:
                    text: 'SETTINGS'
                    font_size: '24sp'
                    size_hint_y: 0.6
                    halign: 'center'
                    valign: 'bottom'
                
                ClockWidget:
                    id: clock
                    font_size: '20sp'
                    size_hint_y: 0.4
                    halign: 'center'
                    valign: 'top'
            
            # WiFi and Settings icons
            BoxLayout:
                size_hint_x: 0.1
                spacing: '10dp'
                Image:
                    source: 'icons/wifi.png'
                    size_hint: 0.5, 0.5
                Image:
                    source: 'icons/settings.png'
                    size_hint: 0.5, 0.5

        # Main content area
        BoxLayout:
            orientation: 'horizontal'
            size_hint: 1, 0.9
            pos_hint: {'top': 0.9}
            spacing: 20
            padding: [20, 0, 20, 20]
            
            # Left sidebar with category buttons
            BoxLayout:
                orientation: 'vertical'
                size_hint_x: 0.25
                spacing: 10
                
                # DISPLAY button
                Button:
                    text: 'DISPLAY'
                    font_size: '16sp'
                    size_hint_y: None
                    height: 60
                    background_normal: ''
                    background_color: (0.2, 0.4, 0.6, 1) if root.selected_category == 'DISPLAY' else (0.1, 0.2, 0.3, 1)
                    on_press: root.select_category('DISPLAY')
                    canvas.before:
                        Color:
                            rgba: (0.2, 0.4, 0.6, 1) if root.selected_category == 'DISPLAY' else (0.1, 0.2, 0.3, 1)
                        RoundedRectangle:
                            pos: self.pos
                            size: self.size
                            radius: [10]
                
                # LANGUAGE button
                Button:
                    text: 'LANGUAGE'
                    font_size: '16sp'
                    size_hint_y: None
                    height: 60
                    background_normal: ''
                    background_color: (0.2, 0.4, 0.6, 1) if root.selected_category == 'LANGUAGE' else (0.1, 0.2, 0.3, 1)
                    on_press: root.select_category('LANGUAGE')
                    canvas.before:
                        Color:
                            rgba: (0.2, 0.4, 0.6, 1) if root.selected_category == 'LANGUAGE' else (0.1, 0.2, 0.3, 1)
                        RoundedRectangle:
                            pos: self.pos
                            size: self.size
                            radius: [10]
                
                # SYSTEM button
                Button:
                    text: 'SYSTEM'
                    font_size: '16sp'
                    size_hint_y: None
                    height: 60
                    background_normal: ''
                    background_color: (0.2, 0.4, 0.6, 1) if root.selected_category == 'SYSTEM' else (0.1, 0.2, 0.3, 1)
                    on_press: root.select_category('SYSTEM')
                    canvas.before:
                        Color:
                            rgba: (0.2, 0.4, 0.6, 1) if root.selected_category == 'SYSTEM' else (0.1, 0.2, 0.3, 1)
                        RoundedRectangle:
                            pos: self.pos
                            size: self.size
                            radius: [10]
                
                # SAFETY button
                Button:
                    text: 'SAFETY'
                    font_size: '16sp'
                    size_hint_y: None
                    height: 60
                    background_normal: ''
                    background_color: (0.2, 0.4, 0.6, 1) if root.selected_category == 'SAFETY' else (0.1, 0.2, 0.3, 1)
                    on_press: root.select_category('SAFETY')
                    canvas.before:
                        Color:
                            rgba: (0.2, 0.4, 0.6, 1) if root.selected_category == 'SAFETY' else (0.1, 0.2, 0.3, 1)
                        RoundedRectangle:
                            pos: self.pos
                            size: self.size
                            radius: [10]
                
                # CONNECTION button
                Button:
                    text: 'CONNECTION'
                    font_size: '16sp'
                    size_hint_y: None
                    height: 60
                    background_normal: ''
                    background_color: (0.2, 0.4, 0.6, 1) if root.selected_category == 'CONNECTION' else (0.1, 0.2, 0.3, 1)
                    on_press: root.select_category('CONNECTION')
                    canvas.before:
                        Color:
                            rgba: (0.2, 0.4, 0.6, 1) if root.selected_category == 'CONNECTION' else (0.1, 0.2, 0.3, 1)
                        RoundedRectangle:
                            pos: self.pos
                            size: self.size
                            radius: [10]
                
                # CALIBRATION button
                Button:
                    text: 'CALIBRATION'
                    font_size: '16sp'
                    size_hint_y: None
                    height: 60
                    background_normal: ''
                    background_color: (0.2, 0.4, 0.6, 1) if root.selected_category == 'CALIBRATION' else (0.1, 0.2, 0.3, 1)
                    on_press: root.select_category('CALIBRATION')
                    canvas.before:
                        Color:
                            rgba: (0.2, 0.4, 0.6, 1) if root.selected_category == 'CALIBRATION' else (0.1, 0.2, 0.3, 1)
                        RoundedRectangle:
                            pos: self.pos
                            size: self.size
                            radius: [10]
                
                # Spacer
                Widget:
            
            # Right panel with settings content
            BoxLayout:
                orientation: 'vertical'
                size_hint_x: 0.75
                canvas.before:
                    Color:
                        rgba: 0.08, 0.15, 0.22, 1
                    RoundedRectangle:
                        pos: self.pos
                        size: self.size
                        radius: [15]
                
                # Content area with scrollable view
                ScrollView:
                    do_scroll_x: False
                    do_scroll_y: True
                    
                    BoxLayout:
                        id: settings_content
                        orientation: 'vertical'
                        size_hint_y: None
                        height: self.minimum_height
                        padding: [20, 20]
                        spacing: 15
                        
                        # DISPLAY SETTINGS (visible when DISPLAY is selected)
                        BoxLayout:
                            orientation: 'vertical'
                            size_hint_y: None
                            height: self.minimum_height if root.selected_category == 'DISPLAY' else 0
                            opacity: 1 if root.selected_category == 'DISPLAY' else 0
                            spacing: 15
                            
                            # Brightness setting
                            BoxLayout:
                                orientation: 'vertical'
                                size_hint_y: None
                                height: 80
                                
                                Label:
                                    text: 'Brightness'
                                    font_size: '18sp'
                                    size_hint_y: None
                                    height: 30
                                    halign: 'left'
                                    valign: 'middle'
                                
                                BoxLayout:
                                    orientation: 'horizontal'
                                    size_hint_y: None
                                    height: 50
                                    spacing: 10
                                    
                                    Slider:
                                        id: brightness_slider
                                        min: 10
                                        max: 100
                                        value: root.brightness
                                        on_value: root.set_brightness(self.value)
                                        size_hint_x: 0.8
                                    
                                    Label:
                                        text: f"{int(root.brightness)}%"
                                        font_size: '16sp'
                                        size_hint_x: 0.2
                                        halign: 'center'
                            
                            # Theme setting
                            BoxLayout:
                                orientation: 'horizontal'
                                size_hint_y: None
                                height: 60
                                spacing: 20
                                
                                Label:
                                    text: 'Theme Mode'
                                    font_size: '18sp'
                                    size_hint_x: 0.5
                                    halign: 'left'
                                    valign: 'middle'
                                
                                Button:
                                    text: root.theme_mode
                                    font_size: '16sp'
                                    size_hint_x: 0.5
                                    background_normal: ''
                                    background_color: (0, 0.7, 0, 1) if root.theme_mode == 'Night' else (0.8, 0.6, 0, 1)
                                    on_press: root.toggle_theme()
                                    canvas.before:
                                        Color:
                                            rgba: (0, 0.7, 0, 1) if root.theme_mode == 'Night' else (0.8, 0.6, 0, 1)
                                        RoundedRectangle:
                                            pos: self.pos
                                            size: self.size
                                            radius: [8]
                            
                            # Screen orientation setting
                            BoxLayout:
                                orientation: 'horizontal'
                                size_hint_y: None
                                height: 60
                                spacing: 20
                                
                                Label:
                                    text: 'Screen Orientation'
                                    font_size: '18sp'
                                    size_hint_x: 0.5
                                    halign: 'left'
                                    valign: 'middle'
                                
                                BoxLayout:
                                    orientation: 'horizontal'
                                    size_hint_x: 0.5
                                    spacing: 10
                                    
                                    Button:
                                        text: 'Landscape'
                                        font_size: '14sp'
                                        background_normal: ''
                                        background_color: (0.2, 0.4, 0.6, 1) if root.screen_orientation == 'Landscape' else (0.3, 0.3, 0.3, 1)
                                        on_press: root.set_orientation('Landscape')
                                        canvas.before:
                                            Color:
                                                rgba: (0.2, 0.4, 0.6, 1) if root.screen_orientation == 'Landscape' else (0.3, 0.3, 0.3, 1)
                                            RoundedRectangle:
                                                pos: self.pos
                                                size: self.size
                                                radius: [8]
                                    
                                    Button:
                                        text: 'Portrait'
                                        font_size: '14sp'
                                        background_normal: ''
                                        background_color: (0.2, 0.4, 0.6, 1) if root.screen_orientation == 'Portrait' else (0.3, 0.3, 0.3, 1)
                                        on_press: root.set_orientation('Portrait')
                                        canvas.before:
                                            Color:
                                                rgba: (0.2, 0.4, 0.6, 1) if root.screen_orientation == 'Portrait' else (0.3, 0.3, 0.3, 1)
                                            RoundedRectangle:
                                                pos: self.pos
                                                size: self.size
                                                radius: [8]

                        # LANGUAGE SETTINGS (visible when LANGUAGE is selected)
                        BoxLayout:
                            orientation: 'vertical'
                            size_hint_y: None
                            height: self.minimum_height if root.selected_category == 'LANGUAGE' else 0
                            opacity: 1 if root.selected_category == 'LANGUAGE' else 0
                            spacing: 15

                            # Language selection
                            BoxLayout:
                                orientation: 'horizontal'
                                size_hint_y: None
                                height: 60
                                spacing: 20

                                Label:
                                    text: 'Language'
                                    font_size: '18sp'
                                    size_hint_x: 0.5
                                    halign: 'left'
                                    valign: 'middle'

                                BoxLayout:
                                    orientation: 'horizontal'
                                    size_hint_x: 0.5
                                    spacing: 10

                                    Button:
                                        text: 'English'
                                        font_size: '14sp'
                                        background_normal: ''
                                        background_color: (0.2, 0.4, 0.6, 1) if root.language == 'English' else (0.3, 0.3, 0.3, 1)
                                        on_press: root.set_language('English')
                                        canvas.before:
                                            Color:
                                                rgba: (0.2, 0.4, 0.6, 1) if root.language == 'English' else (0.3, 0.3, 0.3, 1)
                                            RoundedRectangle:
                                                pos: self.pos
                                                size: self.size
                                                radius: [8]

                                    Button:
                                        text: 'Polski'
                                        font_size: '14sp'
                                        background_normal: ''
                                        background_color: (0.2, 0.4, 0.6, 1) if root.language == 'Polski' else (0.3, 0.3, 0.3, 1)
                                        on_press: root.set_language('Polski')
                                        canvas.before:
                                            Color:
                                                rgba: (0.2, 0.4, 0.6, 1) if root.language == 'Polski' else (0.3, 0.3, 0.3, 1)
                                            RoundedRectangle:
                                                pos: self.pos
                                                size: self.size
                                                radius: [8]

                            # Time format setting
                            BoxLayout:
                                orientation: 'horizontal'
                                size_hint_y: None
                                height: 60
                                spacing: 20

                                Label:
                                    text: 'Time Format'
                                    font_size: '18sp'
                                    size_hint_x: 0.5
                                    halign: 'left'
                                    valign: 'middle'

                                Button:
                                    text: root.time_format
                                    font_size: '16sp'
                                    size_hint_x: 0.5
                                    background_normal: ''
                                    background_color: (0.2, 0.4, 0.6, 1)
                                    on_press: root.toggle_time_format()
                                    canvas.before:
                                        Color:
                                            rgba: (0.2, 0.4, 0.6, 1)
                                        RoundedRectangle:
                                            pos: self.pos
                                            size: self.size
                                            radius: [8]

                            # Units setting
                            BoxLayout:
                                orientation: 'horizontal'
                                size_hint_y: None
                                height: 60
                                spacing: 20

                                Label:
                                    text: 'Units'
                                    font_size: '18sp'
                                    size_hint_x: 0.5
                                    halign: 'left'
                                    valign: 'middle'

                                Button:
                                    text: root.units
                                    font_size: '16sp'
                                    size_hint_x: 0.5
                                    background_normal: ''
                                    background_color: (0.2, 0.4, 0.6, 1)
                                    on_press: root.toggle_units()
                                    canvas.before:
                                        Color:
                                            rgba: (0.2, 0.4, 0.6, 1)
                                        RoundedRectangle:
                                            pos: self.pos
                                            size: self.size
                                            radius: [8]

                        # SYSTEM SETTINGS (visible when SYSTEM is selected)
                        BoxLayout:
                            orientation: 'vertical'
                            size_hint_y: None
                            height: self.minimum_height if root.selected_category == 'SYSTEM' else 0
                            opacity: 1 if root.selected_category == 'SYSTEM' else 0
                            spacing: 15

                            # Firmware version (readonly)
                            BoxLayout:
                                orientation: 'horizontal'
                                size_hint_y: None
                                height: 60
                                spacing: 20

                                Label:
                                    text: 'Firmware Version'
                                    font_size: '18sp'
                                    size_hint_x: 0.5
                                    halign: 'left'
                                    valign: 'middle'

                                Label:
                                    text: root.firmware_version
                                    font_size: '16sp'
                                    size_hint_x: 0.5
                                    halign: 'right'
                                    valign: 'middle'
                                    color: 0.7, 0.7, 0.7, 1

                            # Update firmware button
                            Button:
                                text: 'Update Firmware'
                                font_size: '16sp'
                                size_hint_y: None
                                height: 50
                                background_normal: ''
                                background_color: (0.8, 0.6, 0, 1)
                                on_press: root.update_firmware()
                                canvas.before:
                                    Color:
                                        rgba: (0.8, 0.6, 0, 1)
                                    RoundedRectangle:
                                        pos: self.pos
                                        size: self.size
                                        radius: [8]

                            # Restart system button
                            Button:
                                text: 'Restart System'
                                font_size: '16sp'
                                size_hint_y: None
                                height: 50
                                background_normal: ''
                                background_color: (0.6, 0.4, 0, 1)
                                on_press: root.restart_system()
                                canvas.before:
                                    Color:
                                        rgba: (0.6, 0.4, 0, 1)
                                    RoundedRectangle:
                                        pos: self.pos
                                        size: self.size
                                        radius: [8]

                            # Factory reset button
                            Button:
                                text: 'Factory Reset'
                                font_size: '16sp'
                                size_hint_y: None
                                height: 50
                                background_normal: ''
                                background_color: (0.8, 0.2, 0.2, 1)
                                on_press: root.factory_reset()
                                canvas.before:
                                    Color:
                                        rgba: (0.8, 0.2, 0.2, 1)
                                    RoundedRectangle:
                                        pos: self.pos
                                        size: self.size
                                        radius: [8]

                        # SAFETY SETTINGS (visible when SAFETY is selected)
                        BoxLayout:
                            orientation: 'vertical'
                            size_hint_y: None
                            height: self.minimum_height if root.selected_category == 'SAFETY' else 0
                            opacity: 1 if root.selected_category == 'SAFETY' else 0
                            spacing: 15

                            # Settings lock toggle
                            BoxLayout:
                                orientation: 'horizontal'
                                size_hint_y: None
                                height: 60
                                spacing: 20

                                Label:
                                    text: 'Settings Lock (PIN)'
                                    font_size: '18sp'
                                    size_hint_x: 0.5
                                    halign: 'left'
                                    valign: 'middle'

                                Button:
                                    text: 'ON' if root.settings_lock_enabled else 'OFF'
                                    font_size: '16sp'
                                    size_hint_x: 0.5
                                    background_normal: ''
                                    background_color: (0, 0.7, 0, 1) if root.settings_lock_enabled else (0.8, 0.3, 0.3, 1)
                                    on_press: root.toggle_settings_lock()
                                    canvas.before:
                                        Color:
                                            rgba: (0, 0.7, 0, 1) if root.settings_lock_enabled else (0.8, 0.3, 0.3, 1)
                                        RoundedRectangle:
                                            pos: self.pos
                                            size: self.size
                                            radius: [8]

                            # Emergency contact setting
                            BoxLayout:
                                orientation: 'vertical'
                                size_hint_y: None
                                height: 100
                                spacing: 10

                                Label:
                                    text: 'Emergency Contact/Frequency'
                                    font_size: '18sp'
                                    size_hint_y: None
                                    height: 30
                                    halign: 'left'
                                    valign: 'middle'

                                TextInput:
                                    text: root.emergency_contact
                                    font_size: '16sp'
                                    size_hint_y: None
                                    height: 40
                                    multiline: False
                                    on_text: root.set_emergency_contact(self.text)
                                    background_color: 0.1, 0.2, 0.3, 1
                                    foreground_color: 1, 1, 1, 1

                            # Secure mode toggle
                            BoxLayout:
                                orientation: 'horizontal'
                                size_hint_y: None
                                height: 60
                                spacing: 20

                                Label:
                                    text: 'Secure Mode'
                                    font_size: '18sp'
                                    size_hint_x: 0.5
                                    halign: 'left'
                                    valign: 'middle'

                                Button:
                                    text: 'ON' if root.secure_mode else 'OFF'
                                    font_size: '16sp'
                                    size_hint_x: 0.5
                                    background_normal: ''
                                    background_color: (0, 0.7, 0, 1) if root.secure_mode else (0.8, 0.3, 0.3, 1)
                                    on_press: root.toggle_secure_mode()
                                    canvas.before:
                                        Color:
                                            rgba: (0, 0.7, 0, 1) if root.secure_mode else (0.8, 0.3, 0.3, 1)
                                        RoundedRectangle:
                                            pos: self.pos
                                            size: self.size
                                            radius: [8]

                        # CONNECTION SETTINGS (visible when CONNECTION is selected)
                        BoxLayout:
                            orientation: 'vertical'
                            size_hint_y: None
                            height: self.minimum_height if root.selected_category == 'CONNECTION' else 0
                            opacity: 1 if root.selected_category == 'CONNECTION' else 0
                            spacing: 15

                            # WiFi toggle
                            BoxLayout:
                                orientation: 'horizontal'
                                size_hint_y: None
                                height: 60
                                spacing: 20

                                Label:
                                    text: 'WiFi'
                                    font_size: '18sp'
                                    size_hint_x: 0.5
                                    halign: 'left'
                                    valign: 'middle'

                                Button:
                                    text: 'ON' if root.wifi_enabled else 'OFF'
                                    font_size: '16sp'
                                    size_hint_x: 0.5
                                    background_normal: ''
                                    background_color: (0, 0.7, 0, 1) if root.wifi_enabled else (0.8, 0.3, 0.3, 1)
                                    on_press: root.toggle_wifi()
                                    canvas.before:
                                        Color:
                                            rgba: (0, 0.7, 0, 1) if root.wifi_enabled else (0.8, 0.3, 0.3, 1)
                                        RoundedRectangle:
                                            pos: self.pos
                                            size: self.size
                                            radius: [8]

                            # Bluetooth toggle
                            BoxLayout:
                                orientation: 'horizontal'
                                size_hint_y: None
                                height: 60
                                spacing: 20

                                Label:
                                    text: 'Bluetooth'
                                    font_size: '18sp'
                                    size_hint_x: 0.5
                                    halign: 'left'
                                    valign: 'middle'

                                Button:
                                    text: 'ON' if root.bluetooth_enabled else 'OFF'
                                    font_size: '16sp'
                                    size_hint_x: 0.5
                                    background_normal: ''
                                    background_color: (0, 0.7, 0, 1) if root.bluetooth_enabled else (0.8, 0.3, 0.3, 1)
                                    on_press: root.toggle_bluetooth()
                                    canvas.before:
                                        Color:
                                            rgba: (0, 0.7, 0, 1) if root.bluetooth_enabled else (0.8, 0.3, 0.3, 1)
                                        RoundedRectangle:
                                            pos: self.pos
                                            size: self.size
                                            radius: [8]

                            # NMEA2000/CANBus ID setting
                            BoxLayout:
                                orientation: 'vertical'
                                size_hint_y: None
                                height: 100
                                spacing: 10

                                Label:
                                    text: 'NMEA2000/CANBus ID'
                                    font_size: '18sp'
                                    size_hint_y: None
                                    height: 30
                                    halign: 'left'
                                    valign: 'middle'

                                TextInput:
                                    text: root.nmea_id
                                    font_size: '16sp'
                                    size_hint_y: None
                                    height: 40
                                    multiline: False
                                    input_filter: 'int'
                                    on_text: root.set_nmea_id(self.text)
                                    background_color: 0.1, 0.2, 0.3, 1
                                    foreground_color: 1, 1, 1, 1

                        # CALIBRATION SETTINGS (visible when CALIBRATION is selected)
                        BoxLayout:
                            orientation: 'vertical'
                            size_hint_y: None
                            height: self.minimum_height if root.selected_category == 'CALIBRATION' else 0
                            opacity: 1 if root.selected_category == 'CALIBRATION' else 0
                            spacing: 15

                            # Compass calibration button
                            Button:
                                text: 'Calibrate Compass'
                                font_size: '16sp'
                                size_hint_y: None
                                height: 50
                                background_normal: ''
                                background_color: (0.2, 0.4, 0.6, 1)
                                on_press: root.calibrate_compass()
                                canvas.before:
                                    Color:
                                        rgba: (0.2, 0.4, 0.6, 1)
                                    RoundedRectangle:
                                        pos: self.pos
                                        size: self.size
                                        radius: [8]

                            # Water sensor zeroing
                            BoxLayout:
                                orientation: 'vertical'
                                size_hint_y: None
                                height: 80

                                Label:
                                    text: 'Water Sensor Zero Point'
                                    font_size: '18sp'
                                    size_hint_y: None
                                    height: 30
                                    halign: 'left'
                                    valign: 'middle'

                                BoxLayout:
                                    orientation: 'horizontal'
                                    size_hint_y: None
                                    height: 50
                                    spacing: 10

                                    Slider:
                                        id: water_zero_slider
                                        min: -10
                                        max: 10
                                        value: root.water_sensor_zero
                                        on_value: root.set_water_zero(self.value)
                                        size_hint_x: 0.8

                                    Label:
                                        text: f"{root.water_sensor_zero:.1f}"
                                        font_size: '16sp'
                                        size_hint_x: 0.2
                                        halign: 'center'

                            # Fuel sensor zeroing
                            BoxLayout:
                                orientation: 'vertical'
                                size_hint_y: None
                                height: 80

                                Label:
                                    text: 'Fuel Sensor Zero Point'
                                    font_size: '18sp'
                                    size_hint_y: None
                                    height: 30
                                    halign: 'left'
                                    valign: 'middle'

                                BoxLayout:
                                    orientation: 'horizontal'
                                    size_hint_y: None
                                    height: 50
                                    spacing: 10

                                    Slider:
                                        id: fuel_zero_slider
                                        min: -10
                                        max: 10
                                        value: root.fuel_sensor_zero
                                        on_value: root.set_fuel_zero(self.value)
                                        size_hint_x: 0.8

                                    Label:
                                        text: f"{root.fuel_sensor_zero:.1f}"
                                        font_size: '16sp'
                                        size_hint_x: 0.2
                                        halign: 'center'

                            # Test actuators button
                            Button:
                                text: 'Test Actuators'
                                font_size: '16sp'
                                size_hint_y: None
                                height: 50
                                background_normal: ''
                                background_color: (0.8, 0.6, 0, 1)
                                on_press: root.test_actuators()
                                canvas.before:
                                    Color:
                                        rgba: (0.8, 0.6, 0, 1)
                                    RoundedRectangle:
                                        pos: self.pos
                                        size: self.size
                                        radius: [8]
