<AlarmScreen>:
    canvas.before:
        Color:
            rgba: 0.05, 0.1, 0.15, 1
        Rectangle:
            pos: self.pos
            size: self.size

    AnchorLayout:
        anchor_y: "top"
        padding: [10, 10, 10, 0]

        BoxLayout:
            orientation: "vertical"
            size_hint: 1, None
            height: self.minimum_height
            spacing: 10

            # CLOCK AT THE TOP
            ClockWidget:
                id: clock
                font_size: 24
                halign: "center"
                valign: "middle"
                size_hint_y: None
                height: 30

            # ALARM TITLE
            Label:
                text: 'ALARM STATUS'
                font_size: '24sp'
                size_hint: None, None
                size: self.texture_size
                pos_hint: {'center_x': 0.5}
                size_hint_y: None
                height: 40

            # SYSTEM STATUS INDICATOR
            BoxLayout:
                orientation: "vertical"
                padding: 10
                size_hint_y: None
                size_hint_x: 0.5
                pos_hint: {'center_x': 0.5}
                height: 25
                canvas.before:
                    Color:
                        rgba: 0.1, 0.2, 0.3, 1 if not app.has_critical_alarms else 0.8, 0.2, 0.2, 1
                    RoundedRectangle:
                        pos: self.pos
                        size: self.size
                        radius: [10]
                Label:
                    text: "SYSTEM STATUS"
                    font_size: 18
                    halign: "center"
                Label:
                    id: system_status
                    text: "OK" if not app.has_active_alarms else "ALERT"
                    font_size: 24
                    halign: "center"
                    color: (0, 1, 0, 1) if not app.has_active_alarms else (1, 0, 0, 1)

            # ALARM LEGEND
            BoxLayout:
                orientation: "horizontal"
                size_hint_y: None
                height: 50
                spacing: 10
                pos_hint: {'center_x': 0.5}
                size_hint_x: 0.9

                # Alarm color legend
                BoxLayout:
                    orientation: "horizontal"
                    size_hint_x: 1
                    spacing: 10

                    # Critical
                    BoxLayout:
                        orientation: "horizontal"
                        size_hint_x: 0.33
                        spacing: 5

                        BoxLayout:
                            size_hint_x: None
                            width: 20
                            canvas.before:
                                Color:
                                    rgba: 0.8, 0.2, 0.2, 1
                                Rectangle:
                                    pos: self.pos
                                    size: self.size

                        Label:
                            text: "Critical"
                            font_size: 14
                            halign: "left"
                            text_size: self.size

                    # Warning
                    BoxLayout:
                        orientation: "horizontal"
                        size_hint_x: 0.33
                        spacing: 5

                        BoxLayout:
                            size_hint_x: None
                            width: 20
                            canvas.before:
                                Color:
                                    rgba: 0.9, 0.7, 0.2, 1
                                Rectangle:
                                    pos: self.pos
                                    size: self.size

                        Label:
                            text: "Warning"
                            font_size: 14
                            halign: "left"
                            text_size: self.size

                    # Information
                    BoxLayout:
                        orientation: "horizontal"
                        size_hint_x: 0.33
                        spacing: 5

                        BoxLayout:
                            size_hint_x: None
                            width: 20
                            canvas.before:
                                Color:
                                    rgba: 0.2, 0.6, 0.8, 1
                                Rectangle:
                                    pos: self.pos
                                    size: self.size

                        Label:
                            text: "Information"
                            font_size: 14
                            halign: "left"
                            text_size: self.size

            # ALARM LIST
            ScrollView:
                size_hint: 0.9, None
                height: 400
                pos_hint: {'center_x': 0.5}

                BoxLayout:
                    id: alarm_list
                    orientation: "vertical"
                    size_hint_y: None
                    height: self.minimum_height
                    spacing: 10
                    padding: 10

            # CRITICAL ALARM CONFIRMATION (BUZZER CONTROL)
            BoxLayout:
                orientation: 'horizontal'
                size_hint_y: None
                height: 60
                spacing: 10
                pos_hint: {'center_x': 0.5}
                size_hint_x: 0.9
                opacity: 1 if app.has_critical_alarms else 0.3

                Button:
                    text: "🔔 CONFIRM CRITICAL ALARMS 🔔"
                    font_size: 20
                    bold: True
                    size_hint_x: 1
                    disabled: not app.has_critical_alarms
                    on_press: app.confirm_critical_alarms()
                    canvas.before:
                        Color:
                            rgba: 0.8, 0.2, 0.2, 1 if app.has_critical_alarms else 0.3, 0.3, 0.3, 1
                        RoundedRectangle:
                            pos: self.pos
                            size: self.size
                            radius: [10]

            # BUZZER STATUS INDICATOR
            BoxLayout:
                orientation: 'horizontal'
                size_hint_y: None
                height: 30
                spacing: 10
                pos_hint: {'center_x': 0.5}
                size_hint_x: 0.9

                Label:
                    text: "🔊 BUZZER ACTIVE" if app.get_buzzer_status() else "🔇 Buzzer Silent"
                    font_size: 16
                    bold: True
                    halign: "center"
                    color: (1, 0.3, 0.3, 1) if app.get_buzzer_status() else (0.5, 0.5, 0.5, 1)

            # STANDARD BUTTONS
            BoxLayout:
                orientation: 'horizontal'
                size_hint_y: None
                height: 50
                spacing: 20
                pos_hint: {'center_x': 0.5}
                size_hint_x: 0.9

                Button:
                    text: "Acknowledge All"
                    size_hint_x: 0.33
                    on_press: root.acknowledge_all_alarms()
                    canvas.before:
                        Color:
                            rgba: 0.3, 0.6, 0.8, 1
                        RoundedRectangle:
                            pos: self.pos
                            size: self.size
                            radius: [5]

                Button:
                    text: "Add Test Alarm"
                    size_hint_x: 0.33
                    on_press: root.add_test_alarm()
                    canvas.before:
                        Color:
                            rgba: 0.6, 0.4, 0.2, 1
                        RoundedRectangle:
                            pos: self.pos
                            size: self.size
                            radius: [5]

                Button:
                    text: "Go to Home"
                    size_hint_x: 0.33
                    on_press: root.go_to_home()
                    canvas.before:
                        Color:
                            rgba: 0.3, 0.6, 0.8, 1
                        RoundedRectangle:
                            pos: self.pos
                            size: self.size
                            radius: [5]

# Template for individual alarm items
<AlarmItem>:
    orientation: "vertical"
    size_hint_y: None
    height: 120
    padding: 10
    spacing: 5

    canvas.before:
        Color:
            rgba: (0.8, 0.2, 0.2, 1) if self.alarm_type == "critical" else ((0.9, 0.7, 0.2, 1) if self.alarm_type == "warning" else (0.2, 0.6, 0.8, 1))
        RoundedRectangle:
            pos: self.pos
            size: self.size
            radius: [10]

    # Alarm header
    BoxLayout:
        orientation: "horizontal"
        size_hint_y: None
        height: 30

        # Alarm type
        Label:
            text: root.alarm_type.upper()
            font_size: 16
            bold: True
            halign: "center"
            size_hint_x: 0.25
            color: (0.8, 0.2, 0.2, 1) if root.alarm_type == "critical" else ((0.9, 0.7, 0.2, 1) if root.alarm_type == "warning" else (0.2, 0.6, 0.8, 1))
            text_size: self.size

        # Alarm source
        Label:
            text: root.alarm_source
            font_size: 18
            bold: True
            halign: "left"
            size_hint_x: 0.3
            text_size: self.size

        # Active status
        Label:
            text: "ACTIVE" if root.alarm_active else "INACTIVE"
            font_size: 14
            bold: True
            halign: "center"
            size_hint_x: 0.2
            color: (1, 0.3, 0.3, 1) if root.alarm_active else (0.5, 0.5, 0.5, 1)
            text_size: self.size

        # Timestamp
        Label:
            text: root.alarm_timestamp
            font_size: 14
            halign: "right"
            size_hint_x: 0.25
            text_size: self.size

    # Alarm content
    BoxLayout:
        orientation: "vertical"
        size_hint_y: None
        height: 50
        padding: [10, 5, 10, 5]

        Label:
            text: root.alarm_message
            font_size: 16
            halign: "left"
            valign: "middle"
            text_size: self.width, None
            size_hint_y: None
            height: self.texture_size[1]

    # Status and buttons
    BoxLayout:
        orientation: "horizontal"
        size_hint_y: None
        height: 30

        # Alarm status
        BoxLayout:
            orientation: "horizontal"
            size_hint_x: 0.4

            Label:
                text: "Status:"
                font_size: 14
                halign: "right"
                size_hint_x: 0.4
                text_size: self.size

            Label:
                text: "ACKNOWLEDGED" if root.alarm_acknowledged else "UNACKNOWLEDGED"
                font_size: 14
                bold: True
                halign: "left"
                color: (0.2, 0.7, 0.2, 1) if root.alarm_acknowledged else (1, 0.3, 0.3, 1)
                size_hint_x: 0.6
                text_size: self.size

        # Acknowledge button
        Button:
            text: "Acknowledge"
            font_size: 14
            size_hint_x: 0.6
            background_color: 0.3, 0.6, 0.9, 1
            disabled: root.alarm_acknowledged
            on_press: app.acknowledge_alarm(root.alarm_id)
