#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
TridentOS Python Startup Script for Raspberry Pi 5
Alternative launcher when bash is not available
"""

import os
import sys
import subprocess
import platform
import time
import json
import shutil
from datetime import datetime
from pathlib import Path

# Colors for terminal output
class Colors:
    RED = '\033[0;31m'
    GREEN = '\033[0;32m'
    YELLOW = '\033[1;33m'
    BLUE = '\033[0;34m'
    PURPLE = '\033[0;35m'
    CYAN = '\033[0;36m'
    WHITE = '\033[1;37m'
    NC = '\033[0m'  # No Color

def log(message, color=Colors.GREEN):
    """Log message with timestamp and color"""
    timestamp = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
    print(f"{color}[{timestamp}] {message}{Colors.NC}")

def warn(message):
    """Warning message"""
    log(f"WARNING: {message}", Colors.YELLOW)

def error(message):
    """Error message"""
    log(f"ERROR: {message}", Colors.RED)

def info(message):
    """Info message"""
    log(f"INFO: {message}", Colors.BLUE)

def print_banner():
    """Print TridentOS banner"""
    try:
        banner = f"""{Colors.BLUE}
╔══════════════════════════════════════════════════════════════╗
║                    TridentOS Marine Control                  ║
║                  Python Startup Script v2.0                 ║
║                     Raspberry Pi 5 Ready                    ║
╚══════════════════════════════════════════════════════════════╝
{Colors.NC}"""
        print(banner)
    except UnicodeEncodeError:
        # Fallback for systems with encoding issues
        print("=" * 60)
        print("         TridentOS Marine Control")
        print("       Python Startup Script v2.0")
        print("        Raspberry Pi 5 Ready")
        print("=" * 60)

def is_raspberry_pi():
    """Enhanced Raspberry Pi detection for all models including RPi5"""
    try:
        # Method 1: Check /proc/cpuinfo for Raspberry Pi
        try:
            with open('/proc/cpuinfo', 'r') as f:
                cpuinfo = f.read()
                if 'Raspberry Pi' in cpuinfo:
                    return True
                # Check for BCM chips (RPi5 uses BCM2712)
                if any(chip in cpuinfo for chip in ['BCM2835', 'BCM2711', 'BCM2712']):
                    return True
        except:
            pass

        # Method 2: Check device tree model
        try:
            with open('/proc/device-tree/model', 'r') as f:
                model = f.read().strip('\x00')
                if 'Raspberry Pi' in model:
                    return True
        except:
            pass

        # Method 3: Check platform info
        machine = platform.machine().lower()
        if machine.startswith('arm') or machine.startswith('aarch'):
            # Additional check for hostname
            hostname = platform.node().lower()
            if 'raspberry' in hostname or 'rpi' in hostname:
                return True

        # Method 4: Check for GPIO paths (common on RPi)
        gpio_paths = ['/sys/class/gpio', '/dev/gpiomem', '/dev/gpiochip0']
        gpio_exists = any(os.path.exists(path) for path in gpio_paths)

        # If we have ARM architecture and GPIO, likely RPi
        if machine.startswith('arm') and gpio_exists:
            return True

        return False
    except Exception as e:
        print(f"Error in Raspberry Pi detection: {e}")
        return False

def check_python_version():
    """Check Python version compatibility"""
    log("Sprawdzanie wersji Python...")
    
    version = sys.version_info
    if version.major >= 3 and version.minor >= 8:
        log(f"✓ Python {version.major}.{version.minor}.{version.micro}")
        return True
    else:
        error(f"Python version too old: {version.major}.{version.minor}.{version.micro}")
        error("Required: Python 3.8 or newer")
        return False

def check_required_files():
    """Check if all required files exist"""
    log("Sprawdzanie wymaganych plików...")
    
    required_files = [
        'main.py',
        'translations.py',
        'ui/settings.kv',
        'widgets/virtual_keyboard.py'
    ]
    
    missing_files = []
    for file_path in required_files:
        if os.path.exists(file_path):
            log(f"✓ Znaleziono: {file_path}")
        else:
            missing_files.append(file_path)
            error(f"Brak pliku: {file_path}")
    
    return len(missing_files) == 0

def setup_directories():
    """Create necessary directories"""
    log("Tworzenie katalogów...")
    
    directories = ['logs', 'backups']
    for directory in directories:
        Path(directory).mkdir(exist_ok=True)
        log(f"✓ Katalog: {directory}")

def install_dependencies():
    """Install required Python packages"""
    log("Sprawdzanie zależności Python...")
    
    # Required packages
    packages = [
        'kivy>=2.3.0',
        'kivymd>=1.1.0'
    ]
    
    # Add Raspberry Pi specific packages
    if is_raspberry_pi():
        packages.extend(['RPi.GPIO', 'gpiozero'])
    
    for package in packages:
        package_name = package.split('>=')[0].split('==')[0]
        try:
            __import__(package_name.replace('-', '_'))
            log(f"✓ {package_name} już zainstalowany")
        except ImportError:
            log(f"Instalowanie {package}...")
            try:
                subprocess.run([
                    sys.executable, '-m', 'pip', 'install', package, '--user'
                ], check=True, capture_output=True)
                log(f"✓ Zainstalowano {package}")
            except subprocess.CalledProcessError as e:
                error(f"Nie udało się zainstalować {package}: {e}")
                return False
    
    return True

def setup_environment():
    """Setup environment variables for Kivy and TridentOS"""
    log("Konfiguracja środowiska...")
    
    # Kivy environment variables
    env_vars = {
        'KIVY_WINDOW_ICON': '',
        'KIVY_NO_CONSOLELOG': '1',
        'KIVY_LOG_LEVEL': 'info',
        'KIVY_WINDOW_FULLSCREEN': '1',
        'KIVY_WINDOW_BORDERLESS': '1'
    }
    
    # Set display if not set
    if not os.environ.get('DISPLAY'):
        os.environ['DISPLAY'] = ':0'
        warn("DISPLAY nie był ustawiony - ustawiono na :0")
    
    for key, value in env_vars.items():
        os.environ[key] = value
        log(f"✓ {key}={value}")

def setup_raspberry_pi_hardware():
    """Setup Raspberry Pi specific hardware"""
    if not is_raspberry_pi():
        warn("Nie na Raspberry Pi - pomijanie konfiguracji sprzętu")
        return True
    
    log("Konfiguracja sprzętu Raspberry Pi...")
    
    # Check brightness control
    brightness_path = '/sys/class/backlight/rpi_backlight/brightness'
    if os.path.exists(brightness_path):
        log("✓ Kontrola jasności dostępna")
        try:
            # Set default brightness to 80% (204/255)
            with open(brightness_path, 'w') as f:
                f.write('204')
            log("✓ Ustawiono domyślną jasność na 80%")
        except PermissionError:
            warn("Brak uprawnień do kontroli jasności")
    else:
        warn("Kontrola jasności niedostępna")
    
    # Check WiFi interface
    try:
        result = subprocess.run(['iwconfig'], capture_output=True, text=True)
        if 'wlan0' in result.stdout:
            log("✓ Interfejs WiFi wlan0 dostępny")
        else:
            warn("Interfejs WiFi wlan0 nie znaleziony")
    except FileNotFoundError:
        warn("iwconfig nie jest dostępny")
    
    # Disable screen saver
    try:
        subprocess.run(['xset', 's', 'off'], check=True, capture_output=True)
        subprocess.run(['xset', '-dpms'], check=True, capture_output=True)
        subprocess.run(['xset', 's', 'noblank'], check=True, capture_output=True)
        log("✓ Wygaszacz ekranu wyłączony")
    except (FileNotFoundError, subprocess.CalledProcessError):
        warn("Nie udało się wyłączyć wygaszacza ekranu")
    
    # Hide mouse cursor
    try:
        subprocess.Popen(['unclutter', '-idle', '1', '-root'])
        log("✓ Kursor myszy ukryty")
    except FileNotFoundError:
        warn("unclutter nie jest zainstalowany")
    
    return True

def backup_settings():
    """Create backup of settings"""
    log("Tworzenie kopii zapasowej ustawień...")
    
    settings_file = 'settings.json'
    if os.path.exists(settings_file):
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        backup_file = f'backups/settings_{timestamp}.json'
        shutil.copy2(settings_file, backup_file)
        log(f"✓ Kopia zapasowa: {backup_file}")
        
        # Keep only 10 most recent backups
        backup_dir = Path('backups')
        backup_files = sorted(backup_dir.glob('settings_*.json'), key=os.path.getmtime, reverse=True)
        for old_backup in backup_files[10:]:
            old_backup.unlink()
            log(f"✓ Usunięto starą kopię: {old_backup}")

def check_version():
    """Check and display version information"""
    log("Sprawdzanie wersji...")
    
    version_file = 'version.txt'
    if os.path.exists(version_file):
        with open(version_file, 'r') as f:
            version_info = f.read().strip()
        log(f"✓ {version_info}")
    else:
        warn("Brak pliku wersji")

def start_tridentos():
    """Start the main TridentOS application"""
    log("🚀 Uruchamianie TridentOS...")
    
    # Create log file
    timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
    log_file = f'logs/tridentos_{timestamp}.log'
    
    log(f"✓ Logi zapisywane do: {log_file}")
    
    try:
        # Start the main application
        with open(log_file, 'w') as f:
            process = subprocess.Popen([
                sys.executable, 'main.py'
            ], stdout=subprocess.PIPE, stderr=subprocess.STDOUT, text=True)
            
            # Real-time log output
            for line in process.stdout:
                print(line.rstrip())
                f.write(line)
                f.flush()
            
            # Wait for process to complete
            exit_code = process.wait()
            
            if exit_code == 0:
                log("✓ TridentOS zakończył działanie normalnie")
            else:
                error(f"TridentOS zakończył działanie z błędem (kod: {exit_code})")
                
                # Show last lines of log on error
                print(f"\n{Colors.RED}Ostatnie linie logu:{Colors.NC}")
                with open(log_file, 'r') as f:
                    lines = f.readlines()
                    for line in lines[-20:]:
                        print(line.rstrip())
            
            return exit_code
            
    except KeyboardInterrupt:
        warn("Przerwano przez użytkownika")
        return 130
    except Exception as e:
        error(f"Błąd uruchamiania: {e}")
        return 1

def cleanup():
    """Cleanup resources"""
    log("Czyszczenie zasobów...")
    
    # Kill unclutter processes
    try:
        subprocess.run(['pkill', 'unclutter'], check=False, capture_output=True)
    except FileNotFoundError:
        pass
    
    # Restore screen settings
    try:
        subprocess.run(['xset', 's', 'on'], check=False, capture_output=True)
        subprocess.run(['xset', '+dpms'], check=False, capture_output=True)
    except FileNotFoundError:
        pass
    
    log("✓ Czyszczenie zakończone")

def main():
    """Main function"""
    print_banner()
    
    try:
        log("Rozpoczynanie uruchamiania TridentOS...")
        
        # Check requirements
        if not check_python_version():
            return 1
        
        if not check_required_files():
            return 1
        
        # Setup environment
        setup_directories()
        setup_environment()
        
        # Install dependencies
        if not install_dependencies():
            return 1
        
        # Hardware setup
        if not setup_raspberry_pi_hardware():
            return 1
        
        # Backup and version check
        backup_settings()
        check_version()
        
        log("✓ Konfiguracja zakończona pomyślnie")
        
        # Start application
        exit_code = start_tridentos()
        
        return exit_code
        
    except KeyboardInterrupt:
        warn("Przerwano przez użytkownika")
        return 130
    except Exception as e:
        error(f"Nieoczekiwany błąd: {e}")
        return 1
    finally:
        cleanup()

if __name__ == "__main__":
    # Handle command line arguments
    if len(sys.argv) > 1:
        arg = sys.argv[1]
        if arg in ['--help', '-h']:
            print("TridentOS Python Startup Script")
            print("Użycie: python3 start_tridentos.py [opcje]")
            print("")
            print("Opcje:")
            print("  --help, -h     Pokaż tę pomoc")
            print("  --version, -v  Pokaż wersję")
            print("  --test         Uruchom w trybie testowym")
            sys.exit(0)
        elif arg in ['--version', '-v']:
            print("TridentOS Python Startup Script v2.0")
            print("Kompatybilny z Raspberry Pi 5")
            sys.exit(0)
        elif arg == '--test':
            print_banner()
            log("Tryb testowy - sprawdzanie konfiguracji...")
            if check_python_version() and check_required_files():
                log("✓ Test konfiguracji zakończony pomyślnie")
                sys.exit(0)
            else:
                error("Test konfiguracji nieudany")
                sys.exit(1)
        else:
            error(f"Nieznany argument: {arg}")
            print("Użyj --help aby zobaczyć dostępne opcje")
            sys.exit(1)
    
    # Normal execution
    exit_code = main()
    
    if exit_code == 0:
        log("🎉 TridentOS zakończył działanie pomyślnie")
    else:
        error("❌ TridentOS zakończył działanie z błędami")
    
    sys.exit(exit_code)
